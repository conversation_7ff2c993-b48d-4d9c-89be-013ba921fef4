package com.trustmarket.chat.presentation.rewardcode

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.trustmarket.chat.data.model.RewardCode
import com.trustmarket.chat.data.model.RewardCodeType
import com.trustmarket.chat.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * ViewModel for Reward Code screen
 */
@HiltViewModel
class RewardCodeViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()

    private val _userPoints = MutableStateFlow(0)
    val userPoints: StateFlow<Int> = _userPoints.asStateFlow()

    private val _redeemHistory = MutableStateFlow<List<RewardCode>>(emptyList())
    val redeemHistory: StateFlow<List<RewardCode>> = _redeemHistory.asStateFlow()

    private val _availableCodes = MutableStateFlow<List<RewardCode>>(emptyList())
    val availableCodes: StateFlow<List<RewardCode>> = _availableCodes.asStateFlow()

    /**
     * Load user data including points and redeem history
     */
    fun loadUserData() {
        viewModelScope.launch {
            try {
                val currentUser = authRepository.getCurrentFirebaseUser()
                if (currentUser != null) {
                    // TODO: Load user points from Firebase
                    _userPoints.value = 150 // Placeholder
                    
                    // TODO: Load redeem history from Firebase
                    loadRedeemHistory()
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحميل بيانات المستخدم"
            }
        }
    }

    /**
     * Load available codes for the user
     */
    fun loadAvailableCodes() {
        viewModelScope.launch {
            try {
                // TODO: Load available codes from Firebase
                // For now, create sample codes
                val sampleCodes = listOf(
                    RewardCode(
                        id = "welcome2024",
                        code = "WELCOME2024",
                        name = "مكافأة الترحيب",
                        description = "مكافأة خاصة للمستخدمين الجدد",
                        points = 100,
                        type = RewardCodeType.POINTS,
                        isActive = true,
                        expiresAt = System.currentTimeMillis() + (30 * 24 * 60 * 60 * 1000L), // 30 days
                        maxUses = 1000,
                        currentUses = 245
                    ),
                    RewardCode(
                        id = "spin2024",
                        code = "SPIN2024",
                        name = "دوران إضافي",
                        description = "احصل على دوران إضافي في عجلة الحظ",
                        points = 0,
                        type = RewardCodeType.LUCKY_WHEEL_SPIN,
                        isActive = true,
                        expiresAt = System.currentTimeMillis() + (7 * 24 * 60 * 60 * 1000L), // 7 days
                        maxUses = 500,
                        currentUses = 123
                    )
                )
                
                // Filter codes that user hasn't used and are still valid
                val currentUser = authRepository.getCurrentFirebaseUser()
                if (currentUser != null) {
                    val validCodes = sampleCodes.filter { code ->
                        code.canUserUse(currentUser.uid, getUserLevel()) && code.isValid()
                    }
                    _availableCodes.value = validCodes
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحميل الأكواد المتاحة"
            }
        }
    }

    /**
     * Redeem a reward code
     */
    fun redeemCode(code: String) {
        viewModelScope.launch {
            if (code.isBlank()) {
                _errorMessage.value = "يرجى إدخال كود صحيح"
                return@launch
            }

            _isLoading.value = true
            _errorMessage.value = null
            _successMessage.value = null

            try {
                // Simulate network delay
                delay(1500)

                val currentUser = authRepository.getCurrentFirebaseUser()
                if (currentUser == null) {
                    _errorMessage.value = "يجب تسجيل الدخول أولاً"
                    return@launch
                }

                // TODO: Validate code with Firebase
                val rewardCode = validateCode(code, currentUser.uid)
                
                if (rewardCode != null) {
                    // Apply reward
                    applyReward(rewardCode)
                    
                    // Add to history
                    addToHistory(rewardCode)
                    
                    // Show success message
                    _successMessage.value = when (rewardCode.type) {
                        RewardCodeType.POINTS -> "تم إضافة ${rewardCode.points} نقطة إلى حسابك!"
                        RewardCodeType.LUCKY_WHEEL_SPIN -> "تم إضافة دوران إضافي لعجلة الحظ!"
                        RewardCodeType.PREMIUM_FEATURE -> "تم تفعيل الميزة المميزة!"
                        RewardCodeType.SPECIAL_BADGE -> "تم إضافة شارة خاصة إلى ملفك الشخصي!"
                        RewardCodeType.CUSTOM_REWARD -> "تم استلام المكافأة الخاصة!"
                    }
                    
                } else {
                    _errorMessage.value = getCodeErrorMessage(code)
                }

            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ أثناء استخدام الكود"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Validate reward code
     */
    private suspend fun validateCode(code: String, userId: String): RewardCode? {
        // TODO: Implement Firebase validation
        // For now, simulate validation with predefined codes
        
        val predefinedCodes = mapOf(
            "WELCOME2024" to RewardCode(
                id = "welcome2024",
                code = "WELCOME2024",
                name = "مكافأة الترحيب",
                description = "مكافأة خاصة للمستخدمين الجدد",
                points = 100,
                type = RewardCodeType.POINTS,
                isActive = true,
                createdAt = System.currentTimeMillis(),
                expiresAt = System.currentTimeMillis() + (30 * 24 * 60 * 60 * 1000L)
            ),
            "SPIN2024" to RewardCode(
                id = "spin2024",
                code = "SPIN2024",
                name = "دوران إضافي",
                description = "احصل على دوران إضافي في عجلة الحظ",
                points = 0,
                type = RewardCodeType.LUCKY_WHEEL_SPIN,
                isActive = true,
                createdAt = System.currentTimeMillis(),
                expiresAt = System.currentTimeMillis() + (7 * 24 * 60 * 60 * 1000L)
            ),
            "POINTS50" to RewardCode(
                id = "points50",
                code = "POINTS50",
                name = "50 نقطة",
                description = "مكافأة نقاط سريعة",
                points = 50,
                type = RewardCodeType.POINTS,
                isActive = true,
                createdAt = System.currentTimeMillis(),
                expiresAt = System.currentTimeMillis() + (14 * 24 * 60 * 60 * 1000L)
            ),
            "TESTCODE" to RewardCode(
                id = "testcode",
                code = "TESTCODE",
                name = "كود تجريبي",
                description = "كود للاختبار",
                points = 25,
                type = RewardCodeType.POINTS,
                isActive = true,
                createdAt = System.currentTimeMillis(),
                expiresAt = System.currentTimeMillis() + (365 * 24 * 60 * 60 * 1000L)
            )
        )

        val rewardCode = predefinedCodes[code.uppercase()]
        
        return if (rewardCode != null && rewardCode.canUserUse(userId, getUserLevel())) {
            rewardCode
        } else {
            null
        }
    }

    /**
     * Apply reward to user
     */
    private suspend fun applyReward(rewardCode: RewardCode) {
        when (rewardCode.type) {
            RewardCodeType.POINTS -> {
                _userPoints.value += rewardCode.points
                // TODO: Update points in Firebase
            }
            RewardCodeType.LUCKY_WHEEL_SPIN -> {
                // TODO: Add extra spin to user's lucky wheel data
            }
            RewardCodeType.PREMIUM_FEATURE -> {
                // TODO: Activate premium feature for user
            }
            RewardCodeType.SPECIAL_BADGE -> {
                // TODO: Add badge to user's profile
            }
            RewardCodeType.CUSTOM_REWARD -> {
                // TODO: Apply custom reward
            }
        }
    }

    /**
     * Add redeemed code to history
     */
    private suspend fun addToHistory(rewardCode: RewardCode) {
        val currentHistory = _redeemHistory.value.toMutableList()
        currentHistory.add(0, rewardCode) // Add to beginning
        
        // Keep only last 50 codes
        if (currentHistory.size > 50) {
            currentHistory.removeAt(currentHistory.size - 1)
        }
        
        _redeemHistory.value = currentHistory
        
        // TODO: Save to Firebase
    }

    /**
     * Load redeem history
     */
    private suspend fun loadRedeemHistory() {
        try {
            // TODO: Load from Firebase
            // For now, create sample history
            val sampleHistory = listOf(
                RewardCode(
                    id = "old1",
                    code = "OLDCODE1",
                    name = "مكافأة قديمة",
                    description = "كود مستخدم سابقاً",
                    points = 75,
                    type = RewardCodeType.POINTS,
                    isActive = false,
                    createdAt = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L) // 7 days ago
                )
            )
            _redeemHistory.value = sampleHistory
        } catch (e: Exception) {
            // Handle error silently for history
        }
    }

    /**
     * Get error message for invalid codes
     */
    private fun getCodeErrorMessage(code: String): String {
        return when {
            code.length < 3 -> "الكود قصير جداً"
            code.length > 20 -> "الكود طويل جداً"
            !code.matches(Regex("[A-Z0-9]+")) -> "الكود يجب أن يحتوي على أحرف وأرقام فقط"
            else -> "كود غير صحيح أو منتهي الصلاحية"
        }
    }

    /**
     * Get user level (for code restrictions)
     */
    private fun getUserLevel(): Int {
        // TODO: Get actual user level from Firebase
        return 1 // Default level
    }

    /**
     * Clear messages
     */
    fun clearMessages() {
        _errorMessage.value = null
        _successMessage.value = null
    }

    /**
     * Check if user has used a specific code
     */
    fun hasUsedCode(code: String): Boolean {
        return _redeemHistory.value.any { it.code.equals(code, ignoreCase = true) }
    }

    /**
     * Get total points earned from codes
     */
    fun getTotalPointsFromCodes(): Int {
        return _redeemHistory.value.sumOf { it.points }
    }

    /**
     * Get codes used count
     */
    fun getCodesUsedCount(): Int {
        return _redeemHistory.value.size
    }

    /**
     * Generate a random code (admin feature)
     */
    fun generateRandomCode(): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return (1..8)
            .map { chars.random() }
            .joinToString("")
    }
}
