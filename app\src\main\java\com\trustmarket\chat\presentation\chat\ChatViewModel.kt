package com.trustmarket.chat.presentation.chat

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseUser
import com.trustmarket.chat.data.model.*
import com.trustmarket.chat.data.repository.AuthRepository
import com.trustmarket.chat.data.repository.ChatRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * ViewModel for Chat screen
 */
@HiltViewModel
class ChatViewModel @Inject constructor(
    private val chatRepository: ChatRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _messages = MutableStateFlow<List<Message>>(emptyList())
    val messages: StateFlow<List<Message>> = _messages.asStateFlow()

    private val _currentRoom = MutableStateFlow<ChatRoom?>(null)
    val currentRoom: StateFlow<ChatRoom?> = _currentRoom.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _currentUser = MutableStateFlow<FirebaseUser?>(null)
    val currentUser: StateFlow<FirebaseUser?> = _currentUser.asStateFlow()

    private val _typingUsers = MutableStateFlow<List<String>>(emptyList())
    val typingUsers: StateFlow<List<String>> = _typingUsers.asStateFlow()

    private val _isConnected = MutableStateFlow(true)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val _replyToMessage = MutableStateFlow<Message?>(null)
    val replyToMessage: StateFlow<Message?> = _replyToMessage.asStateFlow()

    private var currentRoomId: String? = null

    init {
        loadCurrentUser()
        observeConnectionStatus()
    }

    /**
     * Load current user
     */
    private fun loadCurrentUser() {
        _currentUser.value = authRepository.getCurrentFirebaseUser()
    }

    /**
     * Load room information
     */
    fun loadRoom(roomId: String) {
        viewModelScope.launch {
            currentRoomId = roomId
            _isLoading.value = true
            _errorMessage.value = null
            
            try {
                // Join room if not already a member
                val currentUser = authRepository.getCurrentFirebaseUser()
                if (currentUser != null) {
                    chatRepository.joinRoom(roomId, currentUser.uid)
                }
                
                // Load room data (you'll need to implement this in ChatRepository)
                // For now, we'll create a placeholder
                // chatRepository.getRoom(roomId).collect { room ->
                //     _currentRoom.value = room
                // }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحميل الغرفة"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load messages for the room
     */
    fun loadMessages(roomId: String) {
        viewModelScope.launch {
            try {
                chatRepository.getMessages(roomId)
                    .catch { exception ->
                        _errorMessage.value = exception.message ?: "فشل في تحميل الرسائل"
                    }
                    .collect { messageList ->
                        _messages.value = messageList
                    }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ في تحميل الرسائل"
            }
        }
    }

    /**
     * Send a text message
     */
    fun sendMessage(text: String, attachments: List<Attachment> = emptyList()) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val roomId = currentRoomId
            
            if (currentUser == null || roomId == null) {
                _errorMessage.value = "خطأ في إرسال الرسالة"
                return@launch
            }

            if (text.isBlank() && attachments.isEmpty()) {
                return@launch
            }

            try {
                val replyInfo = _replyToMessage.value?.let { replyMsg ->
                    ReplyInfo(
                        messageId = replyMsg.id,
                        senderId = replyMsg.senderId,
                        senderName = replyMsg.senderName,
                        text = replyMsg.text,
                        type = replyMsg.type,
                        timestamp = replyMsg.timestamp
                    )
                }

                if (attachments.isNotEmpty()) {
                    // Send message with attachments
                    for (attachment in attachments) {
                        chatRepository.sendMessageWithAttachment(
                            roomId = roomId,
                            text = text,
                            senderId = currentUser.uid,
                            senderName = currentUser.displayName ?: currentUser.email ?: "مستخدم",
                            senderPhotoUrl = currentUser.photoUrl?.toString(),
                            attachment = attachment,
                            replyTo = replyInfo
                        )
                    }
                } else {
                    // Send text message
                    chatRepository.sendMessage(
                        roomId = roomId,
                        text = text,
                        senderId = currentUser.uid,
                        senderName = currentUser.displayName ?: currentUser.email ?: "مستخدم",
                        senderPhotoUrl = currentUser.photoUrl?.toString(),
                        replyTo = replyInfo
                    )
                }

                // Clear reply
                _replyToMessage.value = null
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في إرسال الرسالة"
            }
        }
    }

    /**
     * Add reaction to message
     */
    fun addReaction(messageId: String, emoji: String) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val roomId = currentRoomId
            
            if (currentUser == null || roomId == null) return@launch

            try {
                chatRepository.addReaction(roomId, messageId, emoji, currentUser.uid)
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في إضافة التفاعل"
            }
        }
    }

    /**
     * Remove reaction from message
     */
    fun removeReaction(messageId: String, emoji: String) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val roomId = currentRoomId
            
            if (currentUser == null || roomId == null) return@launch

            try {
                chatRepository.removeReaction(roomId, messageId, emoji, currentUser.uid)
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في إزالة التفاعل"
            }
        }
    }

    /**
     * Delete message
     */
    fun deleteMessage(messageId: String) {
        viewModelScope.launch {
            val roomId = currentRoomId ?: return@launch

            try {
                chatRepository.deleteMessage(roomId, messageId)
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في حذف الرسالة"
            }
        }
    }

    /**
     * Set typing status
     */
    fun setTyping(isTyping: Boolean) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val roomId = currentRoomId
            
            if (currentUser == null || roomId == null) return@launch

            try {
                // TODO: Implement typing indicator in Firebase
                // This would involve updating a "typing" field in the room
                // and listening for changes from other users
                
                if (isTyping) {
                    // Add user to typing list
                    // firebaseDatabase.getReference("rooms/$roomId/typing/${currentUser.uid}")
                    //     .setValue(System.currentTimeMillis())
                    
                    // Auto-remove after 3 seconds
                    delay(3000)
                    // firebaseDatabase.getReference("rooms/$roomId/typing/${currentUser.uid}")
                    //     .removeValue()
                } else {
                    // Remove user from typing list immediately
                    // firebaseDatabase.getReference("rooms/$roomId/typing/${currentUser.uid}")
                    //     .removeValue()
                }
            } catch (e: Exception) {
                // Handle error silently for typing indicator
            }
        }
    }

    /**
     * Set reply to message
     */
    fun setReplyToMessage(message: Message?) {
        _replyToMessage.value = message
    }

    /**
     * Clear reply
     */
    fun clearReply() {
        _replyToMessage.value = null
    }

    /**
     * Observe connection status
     */
    private fun observeConnectionStatus() {
        viewModelScope.launch {
            // TODO: Implement connection status monitoring
            // This could use Firebase's connection state monitoring
            // or network connectivity monitoring
            
            // For now, assume always connected
            _isConnected.value = true
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Leave room
     */
    fun leaveRoom() {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val roomId = currentRoomId
            
            if (currentUser == null || roomId == null) return@launch

            try {
                chatRepository.leaveRoom(roomId, currentUser.uid)
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في مغادرة الغرفة"
            }
        }
    }

    /**
     * Get current user ID
     */
    fun getCurrentUserId(): String? {
        return authRepository.getCurrentUserId()
    }

    override fun onCleared() {
        super.onCleared()
        // Stop typing when leaving the screen
        setTyping(false)
    }
}
