package com.trustmarket.chat.presentation.theme

import androidx.compose.ui.graphics.Color

// Primary Colors
val Primary = Color(0xFF1976D2)
val PrimaryDark = Color(0xFF1565C0)
val PrimaryLight = Color(0xFFBBDEFB)

// Secondary Colors
val Secondary = Color(0xFFFF9800)
val SecondaryDark = Color(0xFFF57C00)
val SecondaryLight = Color(0xFFFFE0B2)

// Accent Colors
val Accent = Color(0xFF4CAF50)
val AccentDark = Color(0xFF388E3C)
val AccentLight = Color(0xFFC8E6C9)

// Background Colors
val Background = Color(0xFFFAFAFA)
val BackgroundDark = Color(0xFF121212)
val Surface = Color(0xFFFFFFFF)
val SurfaceDark = Color(0xFF1E1E1E)

// Text Colors
val TextPrimary = Color(0xFF212121)
val TextSecondary = Color(0xFF757575)
val TextHint = Color(0xFFBDBDBD)
val TextPrimaryDark = Color(0xFFFFFFFF)
val TextSecondaryDark = Color(0xFFB3B3B3)
val TextHintDark = Color(0xFF666666)

// Chat Colors
val ChatBubbleSent = Color(0xFFE3F2FD)
val ChatBubbleReceived = Color(0xFFF5F5F5)
val ChatBubbleSentDark = Color(0xFF1E3A8A)
val ChatBubbleReceivedDark = Color(0xFF374151)

// Status Colors
val Online = Color(0xFF4CAF50)
val Offline = Color(0xFF9E9E9E)
val Typing = Color(0xFFFF9800)
val Recording = Color(0xFFF44336)

// Error Colors
val Error = Color(0xFFF44336)
val ErrorDark = Color(0xFFD32F2F)
val ErrorLight = Color(0xFFFFCDD2)

// Warning Colors
val Warning = Color(0xFFFF9800)
val WarningDark = Color(0xFFF57C00)
val WarningLight = Color(0xFFFFE0B2)

// Success Colors
val Success = Color(0xFF4CAF50)
val SuccessDark = Color(0xFF388E3C)
val SuccessLight = Color(0xFFC8E6C9)

// Info Colors
val Info = Color(0xFF2196F3)
val InfoDark = Color(0xFF1976D2)
val InfoLight = Color(0xFFBBDEFB)

// Divider Colors
val Divider = Color(0xFFE0E0E0)
val DividerDark = Color(0xFF424242)

// Transparent Colors
val Transparent = Color(0x00000000)
val SemiTransparent = Color(0x80000000)
val SemiTransparentWhite = Color(0x80FFFFFF)

// Lucky Wheel Colors
val WheelColor1 = Color(0xFFFF6B6B)
val WheelColor2 = Color(0xFF4ECDC4)
val WheelColor3 = Color(0xFF45B7D1)
val WheelColor4 = Color(0xFF96CEB4)
val WheelColor5 = Color(0xFFFFEAA7)
val WheelColor6 = Color(0xFFDDA0DD)
val WheelColor7 = Color(0xFF98D8C8)
val WheelColor8 = Color(0xFFF7DC6F)

// Notification Colors
val NotificationBadge = Color(0xFFF44336)
val NotificationBackground = Color(0xFFFFFFFF)

// Material Design Colors
val Black = Color(0xFF000000)
val White = Color(0xFFFFFFFF)
val Gray50 = Color(0xFFFAFAFA)
val Gray100 = Color(0xFFF5F5F5)
val Gray200 = Color(0xFFEEEEEE)
val Gray300 = Color(0xFFE0E0E0)
val Gray400 = Color(0xFFBDBDBD)
val Gray500 = Color(0xFF9E9E9E)
val Gray600 = Color(0xFF757575)
val Gray700 = Color(0xFF616161)
val Gray800 = Color(0xFF424242)
val Gray900 = Color(0xFF212121)
