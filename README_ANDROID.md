# Trust Market Chat - Android App

تطبيق دردشة متطور مبني بـ Kotlin و Jetpack Compose مع Firebase كخدمة خلفية.

## الميزات الرئيسية

### 🔐 المصادقة والأمان
- تسجيل الدخول بـ Google
- مصادقة آمنة مع Firebase Auth
- إدارة جلسات المستخدمين
- حماية البيانات الشخصية

### 💬 الدردشة
- **الدردشة الجماعية**: غرف دردشة متعددة مع إدارة الأعضاء
- **الدردشة الخاصة**: رسائل خاصة بين المستخدمين
- **الرسائل الفورية**: دردشة في الوقت الفعلي
- **مشاركة الملفات**: صور، فيديوهات، مستندات، تسجيلات صوتية (حتى 5MB)
- **الرد على الرسائل**: إمكانية الرد على رسائل محددة
- **التفاعل مع الرسائل**: إضافة ردود أفعال (إيموجي)
- **حذف الرسائل**: حذف الرسائل المرسلة
- **إعادة توجيه الرسائل**: مشاركة الرسائل مع غرف أخرى

### 👥 إدارة المستخدمين
- **الملف الشخصي**: تعديل الاسم والصورة الشخصية والنبذة
- **حالة الاتصال**: عرض حالة المستخدمين (متصل/غير متصل)
- **آخر ظهور**: عرض آخر وقت ظهور للمستخدمين
- **الخصوصية**: إعدادات الخصوصية المتقدمة
- **حظر المستخدمين**: إمكانية حظر المستخدمين المزعجين

### 🏠 إدارة الغرف
- **إنشاء الغرف**: إنشاء غرف دردشة عامة أو خاصة
- **إدارة الأعضاء**: إضافة وإزالة الأعضاء
- **صلاحيات الإدارة**: تعيين مشرفين للغرف
- **إعدادات الغرف**: تخصيص إعدادات كل غرفة
- **الوضع الخاص**: وضع خاص للرسائل المميزة

### 🔔 الإشعارات
- **إشعارات فورية**: إشعارات للرسائل الجديدة
- **إشعارات مخصصة**: إشعارات للرسائل الخاصة والإشارات
- **إعدادات الإشعارات**: تحكم كامل في أنواع الإشعارات
- **الساعات الهادئة**: إيقاف الإشعارات في أوقات محددة

### 🎰 عجلة الحظ
- **دوران يومي**: دوران مجاني يومي لكل مستخدم
- **جوائز متنوعة**: نقاط مكافآت وجوائز خاصة
- **تاريخ الأرباح**: عرض تاريخ الأرباح والجوائز
- **مستويات المستخدمين**: نظام مستويات بناءً على النشاط

### 🎁 أكواد المكافآت
- **فحص الأكواد**: إدخال أكواد المكافآت للحصول على جوائز
- **أنواع مختلفة**: نقاط، دورانات إضافية، ميزات مميزة
- **صلاحية محدودة**: أكواد بصلاحية زمنية محددة
- **استخدام واحد**: أكواد لاستخدام واحد لكل مستخدم

### 👨‍💼 لوحة الإدارة
- **إدارة المستخدمين**: حظر وإلغاء حظر المستخدمين
- **إدارة الغرف**: حذف واستعادة الغرف
- **إدارة المحتوى**: مراقبة ومراجعة المحتوى
- **الإحصائيات**: عرض إحصائيات الاستخدام
- **إدارة المكافآت**: إنشاء وإدارة أكواد المكافآت

## التقنيات المستخدمة

### 📱 Android
- **Kotlin**: لغة البرمجة الأساسية
- **Jetpack Compose**: واجهة المستخدم الحديثة
- **Material Design 3**: تصميم متوافق مع معايير Google
- **Navigation Component**: التنقل بين الشاشات
- **ViewModel & LiveData**: إدارة حالة التطبيق

### 🔥 Firebase
- **Firebase Authentication**: مصادقة المستخدمين
- **Firebase Realtime Database**: قاعدة بيانات فورية
- **Firebase Storage**: تخزين الملفات
- **Firebase Cloud Messaging**: الإشعارات الفورية
- **Firebase Crashlytics**: تتبع الأخطاء

### 🏗️ Architecture
- **MVVM Pattern**: نمط Model-View-ViewModel
- **Repository Pattern**: طبقة البيانات
- **Dependency Injection**: Hilt لحقن التبعيات
- **Clean Architecture**: بنية نظيفة ومنظمة

### 📚 Libraries
- **Hilt**: Dependency Injection
- **Retrofit**: HTTP Client
- **Glide**: تحميل وعرض الصور
- **Room**: قاعدة بيانات محلية
- **Coroutines**: البرمجة غير المتزامنة
- **Flow**: تدفق البيانات التفاعلي

## متطلبات النظام

- **Android 7.0 (API level 24)** أو أعلى
- **2GB RAM** أو أكثر
- **100MB** مساحة تخزين فارغة
- **اتصال بالإنترنت** للمزامنة

## التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/yourusername/trustmarket-chat-android.git
cd trustmarket-chat-android
```

### 2. إعداد Firebase
1. إنشاء مشروع جديد في [Firebase Console](https://console.firebase.google.com/)
2. تفعيل Authentication (Google Sign-In)
3. تفعيل Realtime Database
4. تفعيل Storage
5. تفعيل Cloud Messaging
6. تحميل ملف `google-services.json` ووضعه في مجلد `app/`

### 3. بناء المشروع
```bash
./gradlew build
```

### 4. تشغيل التطبيق
```bash
./gradlew installDebug
```

## البنية المعمارية

```
app/
├── src/main/java/com/trustmarket/chat/
│   ├── data/
│   │   ├── model/          # نماذج البيانات
│   │   └── repository/     # طبقة البيانات
│   ├── di/                 # Dependency Injection
│   ├── presentation/       # طبقة العرض
│   │   ├── auth/          # شاشات المصادقة
│   │   ├── home/          # الشاشة الرئيسية
│   │   ├── chat/          # شاشات الدردشة
│   │   ├── components/    # مكونات مشتركة
│   │   └── theme/         # تصميم التطبيق
│   └── services/          # الخدمات الخلفية
└── src/main/res/          # الموارد
    ├── layout/            # تخطيطات XML
    ├── values/            # القيم والألوان
    └── drawable/          # الصور والرسوم
```

## تشغيل المشروع

1. افتح Android Studio
2. اختر "Open an existing project"
3. اختر مجلد المشروع
4. انتظر حتى يتم تحميل التبعيات
5. قم بتشغيل التطبيق على جهاز أو محاكي

## الميزات المكتملة

✅ **تم إنشاؤها بالكامل:**
- بنية المشروع الأساسية
- إعداد Firebase والتكامل
- نماذج البيانات (User, ChatRoom, Message, Notification)
- Repository classes للتعامل مع Firebase
- Authentication system كامل
- Home screen مع قائمة الغرف
- إنشاء غرف الدردشة
- نظام الإشعارات
- Theme وألوان التطبيق
- Dependency Injection مع Hilt

## الميزات المطلوب إكمالها

🔄 **قيد التطوير:**
- شاشات الدردشة الفعلية
- شاشة الدردشة الخاصة
- شاشة عجلة الحظ
- شاشة أكواد المكافآت
- شاشة الإعدادات
- لوحة الإدارة
- مشاركة الملفات
- تسجيل الصوت

---

**Trust Market Chat** - تطبيق دردشة آمن ومتطور للمجتمع العربي 🚀
