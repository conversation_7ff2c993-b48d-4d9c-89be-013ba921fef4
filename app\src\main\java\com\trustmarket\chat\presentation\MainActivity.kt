package com.trustmarket.chat.presentation

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.navigation.compose.rememberNavController
import com.trustmarket.chat.presentation.auth.AuthActivity
import com.trustmarket.chat.presentation.auth.AuthViewModel
import com.trustmarket.chat.presentation.navigation.Navigation
import com.trustmarket.chat.presentation.theme.TrustMarketChatTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * Main Activity - Entry point of the application
 * Handles authentication state and navigation
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private val authViewModel: AuthViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        // Install splash screen
        installSplashScreen()
        
        super.onCreate(savedInstanceState)

        setContent {
            TrustMarketChatTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainContent()
                }
            }
        }
    }

    @Composable
    private fun MainContent() {
        val authState by authViewModel.authState.collectAsState()
        val navController = rememberNavController()

        LaunchedEffect(authState) {
            when (authState) {
                is AuthViewModel.AuthState.Unauthenticated -> {
                    // Navigate to authentication
                    startActivity(Intent(this@MainActivity, AuthActivity::class.java))
                    finish()
                }
                is AuthViewModel.AuthState.Authenticated -> {
                    // User is authenticated, stay on main activity
                }
                is AuthViewModel.AuthState.Loading -> {
                    // Show loading state
                }
                is AuthViewModel.AuthState.Error -> {
                    // Handle error state
                }
            }
        }

        // Navigation setup
        Navigation(navController = navController)
    }

    override fun onResume() {
        super.onResume()
        // Check authentication state when activity resumes
        authViewModel.checkAuthState()
    }
}
