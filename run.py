#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لتطبيق Trust Market
يمكن تشغيله بدون Firebase للاختبار المحلي
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_cors import CORS
import json
import os
from datetime import datetime
import uuid

# إعداد Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'demo-secret-key-for-testing'
CORS(app)

# إعداد SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# متغيرات عامة للتخزين المؤقت
connected_users = {}
active_rooms = {}
demo_users = {
    '<EMAIL>': {
        'uid': 'admin-001',
        'name': 'المدير العام',
        'email': '<EMAIL>',
        'password': 'admin123'
    },
    '<EMAIL>': {
        'uid': 'user-001', 
        'name': 'مستخدم تجريبي',
        'email': '<EMAIL>',
        'password': 'user123'
    }
}

print("🚀 تشغيل تطبيق Trust Market في الوضع التجريبي")
print("📧 حسابات تجريبية:")
print("   - <EMAIL> / admin123")
print("   - <EMAIL> / user123")

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/login')
def login():
    """صفحة تسجيل الدخول"""
    return render_template('login.html')

@app.route('/chat')
def chat():
    """صفحة الدردشة الرئيسية"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('chat.html')

@app.route('/private-chats')
def private_chats():
    """صفحة الدردشات الخاصة"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('private_chats.html')

@app.route('/settings')
def settings():
    """صفحة الإعدادات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('settings.html')

# API Routes
@app.route('/api/auth/login', methods=['POST'])
def api_login():
    """تسجيل الدخول"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        # التحقق من المستخدمين التجريبيين
        if email in demo_users:
            user = demo_users[email]
            if user['password'] == password:
                session['user_id'] = user['uid']
                session['email'] = user['email']
                session['name'] = user['name']
                
                return jsonify({
                    'success': True,
                    'user': {
                        'uid': user['uid'],
                        'email': user['email'],
                        'name': user['name']
                    }
                })
        
        # إنشاء مستخدم جديد للتجربة
        user_id = str(uuid.uuid4())
        name = data.get('name', email.split('@')[0])
        
        session['user_id'] = user_id
        session['email'] = email
        session['name'] = name
        
        return jsonify({
            'success': True,
            'user': {
                'uid': user_id,
                'email': email,
                'name': name
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/auth/logout', methods=['POST'])
def api_logout():
    """تسجيل الخروج"""
    session.clear()
    return jsonify({'success': True})

@app.route('/api/rooms', methods=['GET'])
def api_get_rooms():
    """الحصول على قائمة الغرف"""
    try:
        rooms = [
            {
                'id': 'arab-group',
                'name': 'كروب عرب',
                'description': 'مجموعة للمتحدثين بالعربية',
                'isPublic': True,
                'memberCount': len([u for u in connected_users.values() if u.get('room') == 'arab-group'])
            },
            {
                'id': 'kurdish-group', 
                'name': 'كروب كردي',
                'description': 'مجموعة للمتحدثين بالكردية',
                'isPublic': True,
                'memberCount': len([u for u in connected_users.values() if u.get('room') == 'kurdish-group'])
            },
            {
                'id': 'general-chat',
                'name': 'الدردشة العامة',
                'description': 'دردشة عامة لجميع المستخدمين',
                'isPublic': True,
                'memberCount': len([u for u in connected_users.values() if u.get('room') == 'general-chat'])
            }
        ]
        return jsonify({'success': True, 'rooms': rooms})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

# Socket Events
@socketio.on('connect')
def on_connect():
    """عند الاتصال"""
    print(f"🔗 مستخدم متصل: {request.sid}")
    
@socketio.on('disconnect')
def on_disconnect():
    """عند قطع الاتصال"""
    print(f"❌ مستخدم منقطع: {request.sid}")
    if request.sid in connected_users:
        user = connected_users[request.sid]
        if user.get('room'):
            leave_room(user['room'])
        del connected_users[request.sid]

@socketio.on('join_room')
def on_join_room(data):
    """الانضمام إلى غرفة"""
    try:
        room_id = data.get('room_id')
        user_info = data.get('user')
        
        if not room_id or not user_info:
            emit('error', {'message': 'بيانات غير صحيحة'})
            return
            
        # إضافة المستخدم إلى الغرفة
        join_room(room_id)
        connected_users[request.sid] = {
            'user_id': user_info.get('uid'),
            'name': user_info.get('name'),
            'email': user_info.get('email'),
            'room': room_id
        }
        
        # إشعار المستخدمين الآخرين
        emit('user_joined', {
            'user': user_info,
            'message': f"{user_info.get('name')} انضم إلى الدردشة"
        }, room=room_id)
        
        print(f"👤 {user_info.get('name')} انضم إلى الغرفة: {room_id}")
        
    except Exception as e:
        emit('error', {'message': f'خطأ في الانضمام: {str(e)}'})

@socketio.on('leave_room')
def on_leave_room(data):
    """مغادرة الغرفة"""
    try:
        room_id = data.get('room_id')
        user_info = data.get('user')
        
        leave_room(room_id)
        
        if request.sid in connected_users:
            connected_users[request.sid]['room'] = None
            
        # إشعار المستخدمين الآخرين
        emit('user_left', {
            'user': user_info,
            'message': f"{user_info.get('name')} غادر الدردشة"
        }, room=room_id)
        
        print(f"👋 {user_info.get('name')} غادر الغرفة: {room_id}")
        
    except Exception as e:
        emit('error', {'message': f'خطأ في المغادرة: {str(e)}'})

@socketio.on('send_message')
def on_send_message(data):
    """إرسال رسالة"""
    try:
        room_id = data.get('room_id')
        message_content = data.get('content')
        user_info = data.get('user')
        
        if not all([room_id, message_content, user_info]):
            emit('error', {'message': 'بيانات الرسالة غير مكتملة'})
            return
            
        # إنشاء الرسالة
        message = {
            'id': str(uuid.uuid4()),
            'content': message_content,
            'author': {
                'uid': user_info.get('uid'),
                'name': user_info.get('name'),
                'email': user_info.get('email')
            },
            'createdAt': datetime.now().isoformat(),
            'room_id': room_id
        }
        
        # إرسال الرسالة لجميع المستخدمين في الغرفة
        emit('new_message', message, room=room_id)
        
        print(f"💬 رسالة جديدة من {user_info.get('name')} في {room_id}: {message_content}")
        
    except Exception as e:
        emit('error', {'message': f'خطأ في إرسال الرسالة: {str(e)}'})

@socketio.on('typing_start')
def on_typing_start(data):
    """بدء الكتابة"""
    try:
        room_id = data.get('room_id')
        user_info = data.get('user')
        
        emit('user_typing', {
            'user': user_info,
            'typing': True
        }, room=room_id, include_self=False)
        
    except Exception as e:
        pass

@socketio.on('typing_stop')
def on_typing_stop(data):
    """توقف الكتابة"""
    try:
        room_id = data.get('room_id')
        user_info = data.get('user')
        
        emit('user_typing', {
            'user': user_info,
            'typing': False
        }, room=room_id, include_self=False)
        
    except Exception as e:
        pass

if __name__ == '__main__':
    print("🚀 بدء تشغيل خادم الدردشة...")
    print("📱 Trust Market - منصة آمنة للبيع والشراء")
    print("🌐 الرابط: http://localhost:5000")
    print("⚠️  هذا إصدار تجريبي بدون Firebase")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
