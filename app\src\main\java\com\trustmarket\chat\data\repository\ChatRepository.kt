package com.trustmarket.chat.data.repository

import com.google.firebase.database.*
import com.google.firebase.storage.FirebaseStorage
import com.trustmarket.chat.data.model.*
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for handling chat operations
 */
@Singleton
class ChatRepository @Inject constructor(
    private val firebaseDatabase: FirebaseDatabase,
    private val firebaseStorage: FirebaseStorage
) {

    /**
     * Get all chat rooms
     */
    fun getChatRooms(): Flow<List<ChatRoom>> = callbackFlow {
        val roomsRef = firebaseDatabase.getReference("rooms")
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val rooms = mutableListOf<ChatRoom>()
                for (roomSnapshot in snapshot.children) {
                    roomSnapshot.getValue(ChatRoom::class.java)?.let { room ->
                        if (room.isActive && !room.isDeleted) {
                            rooms.add(room)
                        }
                    }
                }
                trySend(rooms.sortedByDescending { it.updatedAt })
            }

            override fun onCancelled(error: DatabaseError) {
                close(error.toException())
            }
        }
        roomsRef.addValueEventListener(listener)
        awaitClose { roomsRef.removeEventListener(listener) }
    }

    /**
     * Get messages for a specific room
     */
    fun getMessages(roomId: String): Flow<List<Message>> = callbackFlow {
        val messagesRef = firebaseDatabase.getReference("messages").child(roomId)
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val messages = mutableListOf<Message>()
                for (messageSnapshot in snapshot.children) {
                    messageSnapshot.getValue(Message::class.java)?.let { message ->
                        if (!message.isDeleted) {
                            messages.add(message)
                        }
                    }
                }
                trySend(messages.sortedBy { it.timestamp })
            }

            override fun onCancelled(error: DatabaseError) {
                close(error.toException())
            }
        }
        messagesRef.addValueEventListener(listener)
        awaitClose { messagesRef.removeEventListener(listener) }
    }

    /**
     * Send a text message
     */
    suspend fun sendMessage(
        roomId: String,
        text: String,
        senderId: String,
        senderName: String,
        senderPhotoUrl: String? = null,
        replyTo: ReplyInfo? = null
    ): Result<String> {
        return try {
            val messageId = firebaseDatabase.getReference("messages").child(roomId).push().key
                ?: return Result.failure(Exception("Failed to generate message ID"))

            val message = Message(
                id = messageId,
                text = text,
                senderId = senderId,
                senderName = senderName,
                senderPhotoUrl = senderPhotoUrl,
                roomId = roomId,
                timestamp = System.currentTimeMillis(),
                type = MessageType.TEXT,
                status = MessageStatus.SENT,
                replyTo = replyTo
            )

            // Save message
            firebaseDatabase.getReference("messages").child(roomId).child(messageId)
                .setValue(message).await()

            // Update room's last message
            updateRoomLastMessage(roomId, message)

            Result.success(messageId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Send a message with attachment
     */
    suspend fun sendMessageWithAttachment(
        roomId: String,
        text: String,
        senderId: String,
        senderName: String,
        senderPhotoUrl: String? = null,
        attachment: Attachment,
        replyTo: ReplyInfo? = null
    ): Result<String> {
        return try {
            val messageId = firebaseDatabase.getReference("messages").child(roomId).push().key
                ?: return Result.failure(Exception("Failed to generate message ID"))

            val message = Message(
                id = messageId,
                text = text,
                senderId = senderId,
                senderName = senderName,
                senderPhotoUrl = senderPhotoUrl,
                roomId = roomId,
                timestamp = System.currentTimeMillis(),
                type = getMessageTypeFromAttachment(attachment),
                status = MessageStatus.SENT,
                attachments = listOf(attachment),
                replyTo = replyTo
            )

            // Save message
            firebaseDatabase.getReference("messages").child(roomId).child(messageId)
                .setValue(message).await()

            // Update room's last message
            updateRoomLastMessage(roomId, message)

            Result.success(messageId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Upload file to Firebase Storage
     */
    suspend fun uploadFile(file: File, path: String): Result<String> {
        return try {
            val storageRef = firebaseStorage.getReference(path)
            val uploadTask = storageRef.putFile(android.net.Uri.fromFile(file)).await()
            val downloadUrl = uploadTask.storage.downloadUrl.await()
            Result.success(downloadUrl.toString())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Create a new chat room
     */
    suspend fun createRoom(
        name: String,
        description: String,
        ownerId: String,
        ownerName: String,
        isPrivate: Boolean = false
    ): Result<String> {
        return try {
            val roomId = firebaseDatabase.getReference("rooms").push().key
                ?: return Result.failure(Exception("Failed to generate room ID"))

            val room = ChatRoom(
                id = roomId,
                name = name,
                description = description,
                ownerId = ownerId,
                ownerName = ownerName,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
                isPrivate = isPrivate,
                members = listOf(ownerId),
                admins = listOf(ownerId)
            )

            firebaseDatabase.getReference("rooms").child(roomId).setValue(room).await()
            Result.success(roomId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Join a chat room
     */
    suspend fun joinRoom(roomId: String, userId: String): Result<Unit> {
        return try {
            val roomRef = firebaseDatabase.getReference("rooms").child(roomId)
            val snapshot = roomRef.get().await()
            val room = snapshot.getValue(ChatRoom::class.java)
                ?: return Result.failure(Exception("Room not found"))

            if (!room.members.contains(userId)) {
                val updatedMembers = room.members + userId
                roomRef.child("members").setValue(updatedMembers).await()
                roomRef.child("updatedAt").setValue(System.currentTimeMillis()).await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Leave a chat room
     */
    suspend fun leaveRoom(roomId: String, userId: String): Result<Unit> {
        return try {
            val roomRef = firebaseDatabase.getReference("rooms").child(roomId)
            val snapshot = roomRef.get().await()
            val room = snapshot.getValue(ChatRoom::class.java)
                ?: return Result.failure(Exception("Room not found"))

            val updatedMembers = room.members.filter { it != userId }
            roomRef.child("members").setValue(updatedMembers).await()
            roomRef.child("updatedAt").setValue(System.currentTimeMillis()).await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete a message
     */
    suspend fun deleteMessage(roomId: String, messageId: String): Result<Unit> {
        return try {
            val messageRef = firebaseDatabase.getReference("messages").child(roomId).child(messageId)
            val updates = mapOf(
                "isDeleted" to true,
                "deletedAt" to System.currentTimeMillis()
            )
            messageRef.updateChildren(updates).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Add reaction to message
     */
    suspend fun addReaction(roomId: String, messageId: String, emoji: String, userId: String): Result<Unit> {
        return try {
            val messageRef = firebaseDatabase.getReference("messages").child(roomId).child(messageId)
            val snapshot = messageRef.get().await()
            val message = snapshot.getValue(Message::class.java)
                ?: return Result.failure(Exception("Message not found"))

            val reactions = message.reactions.toMutableMap()
            val emojiReactions = reactions[emoji]?.toMutableList() ?: mutableListOf()
            
            if (!emojiReactions.contains(userId)) {
                emojiReactions.add(userId)
                reactions[emoji] = emojiReactions
                messageRef.child("reactions").setValue(reactions).await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Remove reaction from message
     */
    suspend fun removeReaction(roomId: String, messageId: String, emoji: String, userId: String): Result<Unit> {
        return try {
            val messageRef = firebaseDatabase.getReference("messages").child(roomId).child(messageId)
            val snapshot = messageRef.get().await()
            val message = snapshot.getValue(Message::class.java)
                ?: return Result.failure(Exception("Message not found"))

            val reactions = message.reactions.toMutableMap()
            val emojiReactions = reactions[emoji]?.toMutableList()
            
            if (emojiReactions != null && emojiReactions.contains(userId)) {
                emojiReactions.remove(userId)
                if (emojiReactions.isEmpty()) {
                    reactions.remove(emoji)
                } else {
                    reactions[emoji] = emojiReactions
                }
                messageRef.child("reactions").setValue(reactions).await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update room's last message
     */
    private suspend fun updateRoomLastMessage(roomId: String, message: Message) {
        try {
            val roomRef = firebaseDatabase.getReference("rooms").child(roomId)
            val lastMessage = LastMessage(
                id = message.id,
                text = message.getDisplayText(),
                senderId = message.senderId,
                senderName = message.senderName,
                timestamp = message.timestamp,
                type = message.type
            )
            
            val updates = mapOf(
                "lastMessage" to lastMessage,
                "updatedAt" to System.currentTimeMillis()
            )
            roomRef.updateChildren(updates).await()
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    /**
     * Get message type from attachment
     */
    private fun getMessageTypeFromAttachment(attachment: Attachment): MessageType {
        return when (attachment.type) {
            AttachmentType.IMAGE -> MessageType.IMAGE
            AttachmentType.VIDEO -> MessageType.VIDEO
            AttachmentType.AUDIO -> MessageType.AUDIO
            AttachmentType.DOCUMENT -> MessageType.DOCUMENT
            AttachmentType.FILE -> MessageType.DOCUMENT
        }
    }
}
