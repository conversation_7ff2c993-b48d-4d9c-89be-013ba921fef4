package com.trustmarket.chat.presentation.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseUser
import com.trustmarket.chat.data.model.ChatRoom
import com.trustmarket.chat.data.repository.AuthRepository
import com.trustmarket.chat.data.repository.ChatRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for Home screen
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val chatRepository: ChatRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _chatRooms = MutableStateFlow<List<ChatRoom>>(emptyList())
    val chatRooms: StateFlow<List<ChatRoom>> = _chatRooms.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _currentUser = MutableStateFlow<FirebaseUser?>(null)
    val currentUser: StateFlow<FirebaseUser?> = _currentUser.asStateFlow()

    init {
        loadCurrentUser()
        loadChatRooms()
    }

    /**
     * Load current user
     */
    private fun loadCurrentUser() {
        _currentUser.value = authRepository.getCurrentFirebaseUser()
    }

    /**
     * Load chat rooms
     */
    fun loadChatRooms() {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null
            
            try {
                chatRepository.getChatRooms()
                    .catch { exception ->
                        _errorMessage.value = exception.message ?: "فشل في تحميل الغرف"
                        _isLoading.value = false
                    }
                    .collect { rooms ->
                        _chatRooms.value = rooms
                        _isLoading.value = false
                    }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ غير متوقع"
                _isLoading.value = false
            }
        }
    }

    /**
     * Create a new chat room
     */
    fun createRoom(name: String, description: String, isPrivate: Boolean) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            if (currentUser == null) {
                _errorMessage.value = "يجب تسجيل الدخول أولاً"
                return@launch
            }

            _isLoading.value = true
            
            try {
                val result = chatRepository.createRoom(
                    name = name,
                    description = description,
                    ownerId = currentUser.uid,
                    ownerName = currentUser.displayName ?: currentUser.email ?: "مستخدم",
                    isPrivate = isPrivate
                )
                
                result.fold(
                    onSuccess = { roomId ->
                        // Room created successfully
                        // The rooms list will be updated automatically through the Flow
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "فشل في إنشاء الغرفة"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ أثناء إنشاء الغرفة"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Join a chat room
     */
    fun joinRoom(roomId: String) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            if (currentUser == null) {
                _errorMessage.value = "يجب تسجيل الدخول أولاً"
                return@launch
            }

            try {
                val result = chatRepository.joinRoom(roomId, currentUser.uid)
                result.fold(
                    onSuccess = {
                        // Joined successfully
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "فشل في الانضمام للغرفة"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ أثناء الانضمام للغرفة"
            }
        }
    }

    /**
     * Leave a chat room
     */
    fun leaveRoom(roomId: String) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            if (currentUser == null) {
                _errorMessage.value = "يجب تسجيل الدخول أولاً"
                return@launch
            }

            try {
                val result = chatRepository.leaveRoom(roomId, currentUser.uid)
                result.fold(
                    onSuccess = {
                        // Left successfully
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "فشل في مغادرة الغرفة"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ أثناء مغادرة الغرفة"
            }
        }
    }

    /**
     * Search chat rooms
     */
    fun searchRooms(query: String) {
        viewModelScope.launch {
            if (query.isBlank()) {
                loadChatRooms()
                return@launch
            }

            try {
                chatRepository.getChatRooms()
                    .map { rooms ->
                        rooms.filter { room ->
                            room.name.contains(query, ignoreCase = true) ||
                            room.description.contains(query, ignoreCase = true)
                        }
                    }
                    .collect { filteredRooms ->
                        _chatRooms.value = filteredRooms
                    }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في البحث"
            }
        }
    }

    /**
     * Refresh chat rooms
     */
    fun refresh() {
        loadChatRooms()
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Get current user ID
     */
    fun getCurrentUserId(): String? {
        return authRepository.getCurrentUserId()
    }

    /**
     * Sign out
     */
    fun signOut() {
        viewModelScope.launch {
            try {
                authRepository.signOut()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تسجيل الخروج"
            }
        }
    }
}
