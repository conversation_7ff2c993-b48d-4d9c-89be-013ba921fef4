package com.trustmarket.chat.presentation.privatechat

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseUser
import com.trustmarket.chat.data.model.*
import com.trustmarket.chat.data.repository.AuthRepository
import com.trustmarket.chat.data.repository.ChatRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * ViewModel for Private Chat screen
 */
@HiltViewModel
class PrivateChatViewModel @Inject constructor(
    private val chatRepository: ChatRepository,
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _messages = MutableStateFlow<List<Message>>(emptyList())
    val messages: StateFlow<List<Message>> = _messages.asStateFlow()

    private val _otherUser = MutableStateFlow<User?>(null)
    val otherUser: StateFlow<User?> = _otherUser.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _currentUser = MutableStateFlow<FirebaseUser?>(null)
    val currentUser: StateFlow<FirebaseUser?> = _currentUser.asStateFlow()

    private val _isOtherUserTyping = MutableStateFlow(false)
    val isOtherUserTyping: StateFlow<Boolean> = _isOtherUserTyping.asStateFlow()

    private val _isConnected = MutableStateFlow(true)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val _replyToMessage = MutableStateFlow<Message?>(null)
    val replyToMessage: StateFlow<Message?> = _replyToMessage.asStateFlow()

    private var privateChatRoomId: String? = null
    private var otherUserId: String? = null

    init {
        loadCurrentUser()
        observeConnectionStatus()
    }

    /**
     * Load current user
     */
    private fun loadCurrentUser() {
        _currentUser.value = authRepository.getCurrentFirebaseUser()
    }

    /**
     * Load private chat with another user
     */
    fun loadPrivateChat(otherUserId: String) {
        viewModelScope.launch {
            <EMAIL> = otherUserId
            _isLoading.value = true
            _errorMessage.value = null
            
            try {
                val currentUser = authRepository.getCurrentFirebaseUser()
                if (currentUser == null) {
                    _errorMessage.value = "يجب تسجيل الدخول أولاً"
                    return@launch
                }

                // Load other user information
                loadOtherUser(otherUserId)

                // Create or get private chat room
                val roomId = createOrGetPrivateChatRoom(currentUser.uid, otherUserId)
                privateChatRoomId = roomId

                // Load messages
                if (roomId != null) {
                    loadMessages(roomId)
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحميل المحادثة"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load other user information
     */
    private suspend fun loadOtherUser(userId: String) {
        try {
            // TODO: Implement getUserById in repository
            // For now, create a placeholder user
            val user = User(
                uid = userId,
                name = "مستخدم",
                email = "<EMAIL>",
                isOnline = false,
                lastSeen = System.currentTimeMillis() - 300000 // 5 minutes ago
            )
            _otherUser.value = user
        } catch (e: Exception) {
            // Handle error silently for user info
        }
    }

    /**
     * Create or get private chat room
     */
    private suspend fun createOrGetPrivateChatRoom(currentUserId: String, otherUserId: String): String? {
        return try {
            // Generate consistent room ID for private chats
            val roomId = if (currentUserId < otherUserId) {
                "private_${currentUserId}_${otherUserId}"
            } else {
                "private_${otherUserId}_${currentUserId}"
            }

            // Check if room exists, if not create it
            val result = chatRepository.createRoom(
                name = "Private Chat",
                description = "Private conversation",
                ownerId = currentUserId,
                ownerName = _currentUser.value?.displayName ?: "مستخدم",
                isPrivate = true
            )

            result.fold(
                onSuccess = { createdRoomId -> roomId },
                onFailure = { roomId } // Room might already exist
            )
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Load messages for the private chat
     */
    private fun loadMessages(roomId: String) {
        viewModelScope.launch {
            try {
                chatRepository.getMessages(roomId)
                    .catch { exception ->
                        _errorMessage.value = exception.message ?: "فشل في تحميل الرسائل"
                    }
                    .collect { messageList ->
                        _messages.value = messageList
                    }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ في تحميل الرسائل"
            }
        }
    }

    /**
     * Send a message in private chat
     */
    fun sendMessage(text: String, attachments: List<Attachment> = emptyList()) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val roomId = privateChatRoomId
            
            if (currentUser == null || roomId == null) {
                _errorMessage.value = "خطأ في إرسال الرسالة"
                return@launch
            }

            if (text.isBlank() && attachments.isEmpty()) {
                return@launch
            }

            try {
                val replyInfo = _replyToMessage.value?.let { replyMsg ->
                    ReplyInfo(
                        messageId = replyMsg.id,
                        senderId = replyMsg.senderId,
                        senderName = replyMsg.senderName,
                        text = replyMsg.text,
                        type = replyMsg.type,
                        timestamp = replyMsg.timestamp
                    )
                }

                if (attachments.isNotEmpty()) {
                    // Send message with attachments
                    for (attachment in attachments) {
                        chatRepository.sendMessageWithAttachment(
                            roomId = roomId,
                            text = text,
                            senderId = currentUser.uid,
                            senderName = currentUser.displayName ?: currentUser.email ?: "مستخدم",
                            senderPhotoUrl = currentUser.photoUrl?.toString(),
                            attachment = attachment,
                            replyTo = replyInfo
                        )
                    }
                } else {
                    // Send text message
                    chatRepository.sendMessage(
                        roomId = roomId,
                        text = text,
                        senderId = currentUser.uid,
                        senderName = currentUser.displayName ?: currentUser.email ?: "مستخدم",
                        senderPhotoUrl = currentUser.photoUrl?.toString(),
                        replyTo = replyInfo
                    )
                }

                // Clear reply
                _replyToMessage.value = null
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في إرسال الرسالة"
            }
        }
    }

    /**
     * Add reaction to message
     */
    fun addReaction(messageId: String, emoji: String) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val roomId = privateChatRoomId
            
            if (currentUser == null || roomId == null) return@launch

            try {
                chatRepository.addReaction(roomId, messageId, emoji, currentUser.uid)
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في إضافة التفاعل"
            }
        }
    }

    /**
     * Remove reaction from message
     */
    fun removeReaction(messageId: String, emoji: String) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val roomId = privateChatRoomId
            
            if (currentUser == null || roomId == null) return@launch

            try {
                chatRepository.removeReaction(roomId, messageId, emoji, currentUser.uid)
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في إزالة التفاعل"
            }
        }
    }

    /**
     * Delete message
     */
    fun deleteMessage(messageId: String) {
        viewModelScope.launch {
            val roomId = privateChatRoomId ?: return@launch

            try {
                chatRepository.deleteMessage(roomId, messageId)
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في حذف الرسالة"
            }
        }
    }

    /**
     * Set typing status
     */
    fun setTyping(isTyping: Boolean) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val roomId = privateChatRoomId
            
            if (currentUser == null || roomId == null) return@launch

            try {
                // TODO: Implement typing indicator in Firebase for private chats
                // This would involve updating a "typing" field in the private chat room
                
                if (isTyping) {
                    // Add user to typing list
                    delay(3000) // Auto-remove after 3 seconds
                } else {
                    // Remove user from typing list immediately
                }
            } catch (e: Exception) {
                // Handle error silently for typing indicator
            }
        }
    }

    /**
     * Set reply to message
     */
    fun setReplyToMessage(message: Message?) {
        _replyToMessage.value = message
    }

    /**
     * Clear reply
     */
    fun clearReply() {
        _replyToMessage.value = null
    }

    /**
     * Observe connection status
     */
    private fun observeConnectionStatus() {
        viewModelScope.launch {
            // TODO: Implement connection status monitoring
            _isConnected.value = true
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Block user
     */
    fun blockUser() {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val otherUserId = <EMAIL>
            
            if (currentUser == null || otherUserId == null) return@launch

            try {
                // TODO: Implement user blocking functionality
                // This would involve updating the user's blocked list
                _errorMessage.value = "تم حظر المستخدم"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في حظر المستخدم"
            }
        }
    }

    /**
     * Report user
     */
    fun reportUser(reason: String) {
        viewModelScope.launch {
            val currentUser = authRepository.getCurrentFirebaseUser()
            val otherUserId = <EMAIL>
            
            if (currentUser == null || otherUserId == null) return@launch

            try {
                // TODO: Implement user reporting functionality
                _errorMessage.value = "تم الإبلاغ عن المستخدم"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في الإبلاغ عن المستخدم"
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        // Stop typing when leaving the screen
        setTyping(false)
    }
}
