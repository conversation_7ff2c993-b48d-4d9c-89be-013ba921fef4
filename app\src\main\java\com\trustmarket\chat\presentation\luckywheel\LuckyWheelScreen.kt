package com.trustmarket.chat.presentation.luckywheel

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.center
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.trustmarket.chat.R
import com.trustmarket.chat.data.model.LuckyWheelPrize
import com.trustmarket.chat.data.model.SpinResult
import com.trustmarket.chat.presentation.theme.*
import kotlin.math.*

/**
 * Lucky Wheel screen for spinning and winning prizes
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LuckyWheelScreen(
    navController: NavController,
    viewModel: LuckyWheelViewModel = hiltViewModel()
) {
    val prizes by viewModel.prizes.collectAsState()
    val canSpin by viewModel.canSpin.collectAsState()
    val isSpinning by viewModel.isSpinning.collectAsState()
    val spinResult by viewModel.spinResult.collectAsState()
    val userPoints by viewModel.userPoints.collectAsState()
    val spinHistory by viewModel.spinHistory.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()

    var showResultDialog by remember { mutableStateOf(false) }
    var showHistoryDialog by remember { mutableStateOf(false) }

    // Wheel rotation animation
    var targetRotation by remember { mutableStateOf(0f) }
    val rotation by animateFloatAsState(
        targetValue = targetRotation,
        animationSpec = tween(
            durationMillis = 3000,
            easing = FastOutSlowInEasing
        ),
        finishedListener = {
            if (isSpinning) {
                viewModel.finishSpin()
                showResultDialog = true
            }
        },
        label = "wheel_rotation"
    )

    LaunchedEffect(Unit) {
        viewModel.loadPrizes()
        viewModel.checkCanSpin()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(id = R.string.lucky_wheel)) },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { showHistoryDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.History,
                            contentDescription = "Spin History"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // User Points
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "نقاطك الحالية",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                        Text(
                            text = "$userPoints نقطة",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                    Icon(
                        imageVector = Icons.Default.Stars,
                        contentDescription = null,
                        modifier = Modifier.size(32.dp),
                        tint = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Lucky Wheel
            Box(
                modifier = Modifier.size(300.dp),
                contentAlignment = Alignment.Center
            ) {
                // Wheel
                Canvas(
                    modifier = Modifier
                        .size(280.dp)
                        .rotate(rotation)
                ) {
                    drawLuckyWheel(prizes)
                }

                // Center circle
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Casino,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier.size(24.dp)
                    )
                }

                // Pointer
                Box(
                    modifier = Modifier
                        .offset(y = (-140).dp)
                        .size(20.dp)
                        .clip(
                            GenericShape { size, _ ->
                                moveTo(size.width / 2, 0f)
                                lineTo(0f, size.height)
                                lineTo(size.width, size.height)
                                close()
                            }
                        )
                        .background(MaterialTheme.colorScheme.error)
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Spin Button
            Button(
                onClick = {
                    if (canSpin && !isSpinning) {
                        val randomRotation = (720..1440).random().toFloat() // 2-4 full rotations
                        targetRotation += randomRotation
                        viewModel.spinWheel()
                    }
                },
                enabled = canSpin && !isSpinning,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                shape = RoundedCornerShape(28.dp)
            ) {
                if (isSpinning) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = MaterialTheme.colorScheme.onPrimary,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("جاري الدوران...")
                } else {
                    Icon(
                        imageVector = Icons.Default.Casino,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = if (canSpin) stringResource(id = R.string.spin_wheel) else "تم استخدام الدوران اليومي",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Next spin info
            if (!canSpin) {
                Text(
                    text = "يمكنك الدوران مرة أخرى غداً",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                    textAlign = TextAlign.Center
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Prizes List
            Text(
                text = "الجوائز المتاحة",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(prizes) { prize ->
                    PrizeItem(prize = prize)
                }
            }
        }
    }

    // Result Dialog
    if (showResultDialog && spinResult != null) {
        ResultDialog(
            result = spinResult!!,
            onDismiss = {
                showResultDialog = false
                viewModel.clearResult()
            }
        )
    }

    // History Dialog
    if (showHistoryDialog) {
        HistoryDialog(
            history = spinHistory,
            onDismiss = { showHistoryDialog = false }
        )
    }

    // Error handling
    LaunchedEffect(errorMessage) {
        errorMessage?.let {
            // Show error snackbar
            viewModel.clearError()
        }
    }
}

/**
 * Draw the lucky wheel with prizes
 */
private fun DrawScope.drawLuckyWheel(prizes: List<LuckyWheelPrize>) {
    if (prizes.isEmpty()) return

    val center = size.center
    val radius = size.minDimension / 2
    val anglePerPrize = 360f / prizes.size

    prizes.forEachIndexed { index, prize ->
        val startAngle = index * anglePerPrize
        val sweepAngle = anglePerPrize

        // Draw prize segment
        drawArc(
            color = Color(android.graphics.Color.parseColor(prize.color)),
            startAngle = startAngle,
            sweepAngle = sweepAngle,
            useCenter = true,
            topLeft = Offset(0f, 0f),
            size = androidx.compose.ui.geometry.Size(size.width, size.height)
        )

        // Draw border
        drawArc(
            color = Color.White,
            startAngle = startAngle,
            sweepAngle = sweepAngle,
            useCenter = true,
            style = Stroke(width = 4.dp.toPx()),
            topLeft = Offset(0f, 0f),
            size = androidx.compose.ui.geometry.Size(size.width, size.height)
        )

        // Draw prize text
        rotate(startAngle + sweepAngle / 2, center) {
            drawContext.canvas.nativeCanvas.apply {
                val paint = android.graphics.Paint().apply {
                    color = android.graphics.Color.parseColor(prize.textColor)
                    textSize = 14.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                    isFakeBoldText = true
                }
                drawText(
                    prize.name,
                    center.x,
                    center.y - radius * 0.6f,
                    paint
                )
                drawText(
                    "${prize.points} نقطة",
                    center.x,
                    center.y - radius * 0.4f,
                    paint
                )
            }
        }
    }
}

/**
 * Prize item in the list
 */
@Composable
private fun PrizeItem(prize: LuckyWheelPrize) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(android.graphics.Color.parseColor(prize.color)).copy(alpha = 0.1f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(Color(android.graphics.Color.parseColor(prize.color))),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "${prize.points}",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(android.graphics.Color.parseColor(prize.textColor)),
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = prize.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                if (prize.description.isNotBlank()) {
                    Text(
                        text = prize.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }
            }

            Text(
                text = "${(prize.probability * 100).toInt()}%",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
    }
}

/**
 * Result dialog showing spin result
 */
@Composable
private fun ResultDialog(
    result: SpinResult,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = if (result.isWin) stringResource(id = R.string.congratulations) else stringResource(id = R.string.try_again),
                textAlign = TextAlign.Center
            )
        },
        text = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = if (result.isWin) Icons.Default.EmojiEvents else Icons.Default.SentimentDissatisfied,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = if (result.isWin) Color(0xFFFFD700) else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
                Spacer(modifier = Modifier.height(16.dp))
                if (result.isWin) {
                    Text(
                        text = "${stringResource(id = R.string.you_won)} ${result.reward}",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "+${result.points} نقطة",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary,
                        textAlign = TextAlign.Center
                    )
                } else {
                    Text(
                        text = "حظ أوفر في المرة القادمة!",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                }
            }
        },
        confirmButton = {
            Button(onClick = onDismiss) {
                Text(stringResource(id = R.string.ok))
            }
        }
    )
}

/**
 * History dialog showing spin history
 */
@Composable
private fun HistoryDialog(
    history: List<SpinResult>,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("تاريخ الدورانات") },
        text = {
            LazyColumn(
                modifier = Modifier.height(300.dp)
            ) {
                items(history) { result ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column {
                                Text(
                                    text = result.reward,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Medium
                                )
                                Text(
                                    text = java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault())
                                        .format(java.util.Date(result.date)),
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                            }
                            Text(
                                text = if (result.isWin) "+${result.points}" else "0",
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (result.isWin) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(onClick = onDismiss) {
                Text(stringResource(id = R.string.ok))
            }
        }
    )
}
