package com.trustmarket.chat.presentation.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.trustmarket.chat.data.model.*
import com.trustmarket.chat.presentation.theme.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * Message item component for displaying individual messages
 */
@OptIn(ExperimentalFoundationApi::class, ExperimentalGlideComposeApi::class)
@Composable
fun MessageItem(
    message: Message,
    currentUserId: String,
    onMessageClick: () -> Unit,
    onMessageLongClick: () -> Unit,
    onReactionClick: (String) -> Unit,
    onReplyClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isOwnMessage = message.isSentBy(currentUserId)
    val bubbleColor = if (isOwnMessage) {
        MaterialTheme.colorScheme.primary
    } else {
        MaterialTheme.colorScheme.surfaceVariant
    }
    val textColor = if (isOwnMessage) {
        MaterialTheme.colorScheme.onPrimary
    } else {
        MaterialTheme.colorScheme.onSurfaceVariant
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 2.dp)
    ) {
        // Show date separator if needed
        // TODO: Add date separator logic

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = if (isOwnMessage) Arrangement.End else Arrangement.Start
        ) {
            if (!isOwnMessage) {
                // Sender avatar
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)),
                    contentAlignment = Alignment.Center
                ) {
                    if (message.senderPhotoUrl != null) {
                        GlideImage(
                            model = message.senderPhotoUrl,
                            contentDescription = "Sender Avatar",
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape),
                            contentScale = ContentScale.Crop
                        )
                    } else {
                        Text(
                            text = message.senderName.take(1).uppercase(),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
                Spacer(modifier = Modifier.width(8.dp))
            }

            // Message bubble
            Column(
                modifier = Modifier.widthIn(max = 280.dp),
                horizontalAlignment = if (isOwnMessage) Alignment.End else Alignment.Start
            ) {
                // Sender name (for received messages)
                if (!isOwnMessage) {
                    Text(
                        text = message.senderName,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(start = 12.dp, bottom = 2.dp)
                    )
                }

                // Reply indicator
                message.replyTo?.let { replyInfo ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 4.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(8.dp)
                        ) {
                            Text(
                                text = "رد على ${replyInfo.senderName}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = replyInfo.text,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                                maxLines = 2,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                    }
                }

                // Message content
                Card(
                    modifier = Modifier
                        .combinedClickable(
                            onClick = onMessageClick,
                            onLongClick = onMessageLongClick
                        ),
                    colors = CardDefaults.cardColors(containerColor = bubbleColor),
                    shape = RoundedCornerShape(
                        topStart = if (isOwnMessage) 16.dp else 4.dp,
                        topEnd = if (isOwnMessage) 4.dp else 16.dp,
                        bottomStart = 16.dp,
                        bottomEnd = 16.dp
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        // Message text
                        if (message.text.isNotBlank()) {
                            Text(
                                text = message.text,
                                style = MaterialTheme.typography.bodyMedium,
                                color = textColor
                            )
                        }

                        // Attachments
                        message.attachments.forEach { attachment ->
                            Spacer(modifier = Modifier.height(8.dp))
                            AttachmentView(
                                attachment = attachment,
                                textColor = textColor
                            )
                        }

                        // Message info (time, status)
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 4.dp),
                            horizontalArrangement = Arrangement.End,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Edited indicator
                            if (message.isEdited) {
                                Text(
                                    text = "معدل",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = textColor.copy(alpha = 0.7f),
                                    fontSize = 10.sp
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                            }

                            // Time
                            Text(
                                text = message.getFormattedTime(),
                                style = MaterialTheme.typography.bodySmall,
                                color = textColor.copy(alpha = 0.7f),
                                fontSize = 10.sp
                            )

                            // Message status (for own messages)
                            if (isOwnMessage) {
                                Spacer(modifier = Modifier.width(4.dp))
                                Icon(
                                    imageVector = when (message.status) {
                                        MessageStatus.SENDING -> Icons.Default.Schedule
                                        MessageStatus.SENT -> Icons.Default.Done
                                        MessageStatus.DELIVERED -> Icons.Default.DoneAll
                                        MessageStatus.READ -> Icons.Default.DoneAll
                                        MessageStatus.FAILED -> Icons.Default.Error
                                    },
                                    contentDescription = null,
                                    modifier = Modifier.size(12.dp),
                                    tint = when (message.status) {
                                        MessageStatus.READ -> Color(0xFF4CAF50)
                                        MessageStatus.DELIVERED -> textColor.copy(alpha = 0.7f)
                                        MessageStatus.FAILED -> Color(0xFFF44336)
                                        else -> textColor.copy(alpha = 0.7f)
                                    }
                                )
                            }
                        }
                    }
                }

                // Reactions
                if (message.reactions.isNotEmpty()) {
                    LazyRow(
                        modifier = Modifier.padding(top = 4.dp),
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        items(message.reactions.entries.toList()) { (emoji, userIds) ->
                            if (userIds.isNotEmpty()) {
                                ReactionChip(
                                    emoji = emoji,
                                    count = userIds.size,
                                    isSelected = userIds.contains(currentUserId),
                                    onClick = { onReactionClick(emoji) }
                                )
                            }
                        }
                    }
                }
            }

            if (isOwnMessage) {
                Spacer(modifier = Modifier.width(40.dp))
            }
        }
    }
}

/**
 * Attachment view component
 */
@Composable
private fun AttachmentView(
    attachment: Attachment,
    textColor: Color
) {
    when (attachment.type) {
        AttachmentType.IMAGE -> {
            GlideImage(
                model = attachment.url,
                contentDescription = "Image",
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 200.dp)
                    .clip(RoundedCornerShape(8.dp)),
                contentScale = ContentScale.Crop
            )
        }
        AttachmentType.VIDEO -> {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(Color.Black.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "Play Video",
                    modifier = Modifier.size(48.dp),
                    tint = textColor
                )
            }
        }
        AttachmentType.AUDIO -> {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.AudioFile,
                    contentDescription = "Audio",
                    tint = textColor
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = attachment.name,
                        style = MaterialTheme.typography.bodySmall,
                        color = textColor,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    if (attachment.duration > 0) {
                        Text(
                            text = formatDuration(attachment.duration),
                            style = MaterialTheme.typography.bodySmall,
                            color = textColor.copy(alpha = 0.7f),
                            fontSize = 10.sp
                        )
                    }
                }
            }
        }
        AttachmentType.DOCUMENT, AttachmentType.FILE -> {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.InsertDriveFile,
                    contentDescription = "Document",
                    tint = textColor
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = attachment.name,
                        style = MaterialTheme.typography.bodySmall,
                        color = textColor,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = formatFileSize(attachment.size),
                        style = MaterialTheme.typography.bodySmall,
                        color = textColor.copy(alpha = 0.7f),
                        fontSize = 10.sp
                    )
                }
            }
        }
    }
}

/**
 * Reaction chip component
 */
@Composable
private fun ReactionChip(
    emoji: String,
    count: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.combinedClickable(onClick = onClick),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = emoji,
                fontSize = 12.sp
            )
            if (count > 1) {
                Spacer(modifier = Modifier.width(2.dp))
                Text(
                    text = count.toString(),
                    style = MaterialTheme.typography.bodySmall,
                    fontSize = 10.sp,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    }
                )
            }
        }
    }
}

/**
 * Format duration in milliseconds to readable format
 */
private fun formatDuration(durationMs: Long): String {
    val seconds = durationMs / 1000
    val minutes = seconds / 60
    val remainingSeconds = seconds % 60
    return String.format("%d:%02d", minutes, remainingSeconds)
}

/**
 * Format file size in bytes to readable format
 */
private fun formatFileSize(sizeBytes: Long): String {
    val kb = sizeBytes / 1024.0
    val mb = kb / 1024.0
    
    return when {
        mb >= 1 -> String.format("%.1f MB", mb)
        kb >= 1 -> String.format("%.1f KB", kb)
        else -> "$sizeBytes B"
    }
}
