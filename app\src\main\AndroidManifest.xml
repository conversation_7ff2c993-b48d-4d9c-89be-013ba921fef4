<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <!-- Camera feature -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.microphone"
        android:required="false" />

    <application
        android:name=".TrustMarketApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:theme="@style/Theme.TrustMarketChat"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- Main Activity -->
        <activity
            android:name=".presentation.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.TrustMarketChat"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Authentication Activity -->
        <activity
            android:name=".presentation.auth.AuthActivity"
            android:exported="false"
            android:theme="@style/Theme.TrustMarketChat" />

        <!-- Chat Activity -->
        <activity
            android:name=".presentation.chat.ChatActivity"
            android:exported="false"
            android:theme="@style/Theme.TrustMarketChat"
            android:windowSoftInputMode="adjustResize" />

        <!-- Private Chat Activity -->
        <activity
            android:name=".presentation.privatechat.PrivateChatActivity"
            android:exported="false"
            android:theme="@style/Theme.TrustMarketChat"
            android:windowSoftInputMode="adjustResize" />

        <!-- Settings Activity -->
        <activity
            android:name=".presentation.settings.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.TrustMarketChat" />

        <!-- Lucky Wheel Activity -->
        <activity
            android:name=".presentation.luckywheel.LuckyWheelActivity"
            android:exported="false"
            android:theme="@style/Theme.TrustMarketChat" />

        <!-- Notifications Activity -->
        <activity
            android:name=".presentation.notifications.NotificationsActivity"
            android:exported="false"
            android:theme="@style/Theme.TrustMarketChat" />

        <!-- Firebase Messaging Service -->
        <service
            android:name=".services.FirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- File Provider -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Firebase Messaging default notification icon -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <!-- Firebase Messaging default notification color -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/primary" />

        <!-- Firebase Messaging default notification channel -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" />

    </application>

</manifest>
