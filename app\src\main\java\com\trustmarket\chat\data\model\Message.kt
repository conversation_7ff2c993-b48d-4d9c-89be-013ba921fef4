package com.trustmarket.chat.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Message data model
 */
@Parcelize
data class Message(
    val id: String = "",
    val text: String = "",
    val senderId: String = "",
    val senderName: String = "",
    val senderPhotoUrl: String? = null,
    val roomId: String = "",
    val timestamp: Long = 0L,
    val type: MessageType = MessageType.TEXT,
    val status: MessageStatus = MessageStatus.SENT,
    val isEdited: Boolean = false,
    val editedAt: Long = 0L,
    val isDeleted: Boolean = false,
    val deletedAt: Long = 0L,
    val replyTo: ReplyInfo? = null,
    val forwardFrom: ForwardInfo? = null,
    val attachments: List<Attachment> = emptyList(),
    val reactions: Map<String, List<String>> = emptyMap(), // emoji -> list of user IDs
    val mentions: List<String> = emptyList(), // list of mentioned user IDs
    val readBy: Map<String, Long> = emptyMap(), // userId -> timestamp
    val deliveredTo: Map<String, Long> = emptyMap(), // userId -> timestamp
    val metadata: MessageMetadata = MessageMetadata(),
    val isSpecial: Boolean = false,
    val specialData: SpecialMessageData? = null
) : Parcelable {
    
    /**
     * Check if message is sent by specific user
     */
    fun isSentBy(userId: String): Boolean {
        return senderId == userId
    }
    
    /**
     * Check if message is read by specific user
     */
    fun isReadBy(userId: String): Boolean {
        return readBy.containsKey(userId)
    }
    
    /**
     * Check if message is delivered to specific user
     */
    fun isDeliveredTo(userId: String): Boolean {
        return deliveredTo.containsKey(userId)
    }
    
    /**
     * Get formatted timestamp
     */
    fun getFormattedTime(): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        val minutes = diff / (1000 * 60)
        val hours = minutes / 60
        val days = hours / 24
        
        return when {
            minutes < 1 -> "الآن"
            minutes < 60 -> "${minutes}د"
            hours < 24 -> "${hours}س"
            days < 7 -> "${days}ي"
            else -> {
                val date = java.util.Date(timestamp)
                java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault()).format(date)
            }
        }
    }
    
    /**
     * Get display text based on message type
     */
    fun getDisplayText(): String {
        return when (type) {
            MessageType.TEXT -> text
            MessageType.IMAGE -> "📷 صورة"
            MessageType.VIDEO -> "🎥 فيديو"
            MessageType.AUDIO -> "🎵 تسجيل صوتي"
            MessageType.DOCUMENT -> "📄 مستند"
            MessageType.LOCATION -> "📍 موقع"
            MessageType.CONTACT -> "👤 جهة اتصال"
            MessageType.STICKER -> "😊 ملصق"
            MessageType.GIF -> "🎬 GIF"
            MessageType.SPECIAL -> specialData?.title ?: "رسالة خاصة"
            MessageType.SYSTEM -> text
            MessageType.REPLY -> text
            MessageType.FORWARD -> text
        }
    }
    
    /**
     * Check if message has attachments
     */
    fun hasAttachments(): Boolean {
        return attachments.isNotEmpty()
    }
    
    /**
     * Get reaction count for specific emoji
     */
    fun getReactionCount(emoji: String): Int {
        return reactions[emoji]?.size ?: 0
    }
    
    /**
     * Check if user reacted with specific emoji
     */
    fun hasUserReacted(userId: String, emoji: String): Boolean {
        return reactions[emoji]?.contains(userId) ?: false
    }
}

/**
 * Message status enum
 */
enum class MessageStatus {
    SENDING,
    SENT,
    DELIVERED,
    READ,
    FAILED
}

/**
 * Reply information
 */
@Parcelize
data class ReplyInfo(
    val messageId: String = "",
    val senderId: String = "",
    val senderName: String = "",
    val text: String = "",
    val type: MessageType = MessageType.TEXT,
    val timestamp: Long = 0L
) : Parcelable

/**
 * Forward information
 */
@Parcelize
data class ForwardInfo(
    val originalSenderId: String = "",
    val originalSenderName: String = "",
    val originalRoomId: String = "",
    val originalRoomName: String = "",
    val forwardedBy: String = "",
    val forwardedAt: Long = 0L,
    val forwardCount: Int = 1
) : Parcelable

/**
 * Message attachment
 */
@Parcelize
data class Attachment(
    val id: String = "",
    val name: String = "",
    val url: String = "",
    val localPath: String? = null,
    val type: AttachmentType = AttachmentType.FILE,
    val size: Long = 0L,
    val mimeType: String = "",
    val thumbnailUrl: String? = null,
    val duration: Long = 0L, // for audio/video files
    val width: Int = 0, // for images/videos
    val height: Int = 0, // for images/videos
    val isUploading: Boolean = false,
    val uploadProgress: Float = 0f,
    val isDownloading: Boolean = false,
    val downloadProgress: Float = 0f
) : Parcelable

/**
 * Attachment types
 */
enum class AttachmentType {
    FILE,
    IMAGE,
    VIDEO,
    AUDIO,
    DOCUMENT
}

/**
 * Message metadata
 */
@Parcelize
data class MessageMetadata(
    val deviceInfo: String = "",
    val appVersion: String = "",
    val location: LocationInfo? = null,
    val isEncrypted: Boolean = false,
    val encryptionKey: String? = null,
    val priority: MessagePriority = MessagePriority.NORMAL,
    val tags: List<String> = emptyList()
) : Parcelable

/**
 * Location information
 */
@Parcelize
data class LocationInfo(
    val latitude: Double = 0.0,
    val longitude: Double = 0.0,
    val address: String = "",
    val accuracy: Float = 0f
) : Parcelable

/**
 * Message priority
 */
enum class MessagePriority {
    LOW,
    NORMAL,
    HIGH,
    URGENT
}

/**
 * Special message data
 */
@Parcelize
data class SpecialMessageData(
    val title: String = "",
    val subtitle: String = "",
    val imageUrl: String? = null,
    val actionText: String = "",
    val actionUrl: String = "",
    val backgroundColor: String = "#FFFFFF",
    val textColor: String = "#000000",
    val isPromoted: Boolean = false,
    val expiresAt: Long = 0L,
    val targetAudience: List<String> = emptyList()
) : Parcelable
