/**
 * ملف JavaScript الرئيسي لتطبيق Trust Market
 */

// متغيرات عامة
let currentTheme = 'dark';
let notificationPermission = false;

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق
function initializeApp() {
    // تطبيق الثيم
    applyTheme();
    
    // طلب إذن الإشعارات
    requestNotificationPermission();
    
    // تهيئة التنقل
    initializeNavigation();
    
    // تهيئة الأحداث العامة
    initializeGlobalEvents();
}

// تطبيق الثيم
function applyTheme() {
    document.body.classList.add('dark-theme');
}

// طلب إذن الإشعارات
function requestNotificationPermission() {
    if ('Notification' in window) {
        Notification.requestPermission().then(function(permission) {
            notificationPermission = permission === 'granted';
        });
    }
}

// تهيئة التنقل
function initializeNavigation() {
    // تحديد الصفحة النشطة في التنقل
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link, .bottom-nav-item');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href.replace('/', ''))) {
            link.classList.add('active');
        }
    });
}

// تهيئة الأحداث العامة
function initializeGlobalEvents() {
    // معالجة أخطاء JavaScript
    window.addEventListener('error', function(e) {
        console.error('خطأ JavaScript:', e.error);
    });
    
    // معالجة الأخطاء غير المعالجة في Promise
    window.addEventListener('unhandledrejection', function(e) {
        console.error('خطأ Promise غير معالج:', e.reason);
    });
    
    // معالجة تغيير حالة الاتصال
    window.addEventListener('online', function() {
        showToast('تم استعادة الاتصال بالإنترنت', 'success');
    });
    
    window.addEventListener('offline', function() {
        showToast('انقطع الاتصال بالإنترنت', 'warning');
    });
}

// دوال مساعدة للإشعارات
function showNotification(title, body, icon = '/static/images/logo.png') {
    if (notificationPermission && document.hidden) {
        new Notification(title, {
            body: body,
            icon: icon,
            dir: 'rtl',
            lang: 'ar'
        });
    }
}

// دوال مساعدة للتاريخ والوقت
function formatDate(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
}

function formatTime(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    }).format(new Date(date));
}

function formatDateTime(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    }).format(new Date(date));
}

function getRelativeTime(date) {
    const now = new Date();
    const messageDate = new Date(date);
    const diffInSeconds = Math.floor((now - messageDate) / 1000);
    
    if (diffInSeconds < 60) {
        return 'الآن';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `منذ ${days} يوم`;
    }
}

// دوال مساعدة للنصوص
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function truncateText(text, maxLength = 100) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

// دوال مساعدة للتحقق من صحة البيانات
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPassword(password) {
    // كلمة المرور يجب أن تكون 6 أحرف على الأقل
    return password.length >= 6;
}

// دوال مساعدة للتخزين المحلي
function setLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
    } catch (error) {
        console.error('خطأ في حفظ البيانات محلياً:', error);
        return false;
    }
}

function getLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error('خطأ في قراءة البيانات المحلية:', error);
        return defaultValue;
    }
}

function removeLocalStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('خطأ في حذف البيانات المحلية:', error);
        return false;
    }
}

// دوال مساعدة للطلبات HTTP
async function makeRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return { success: true, data };
    } catch (error) {
        console.error('خطأ في الطلب:', error);
        return { success: false, error: error.message };
    }
}

// دوال مساعدة للواجهة
function showLoading(element) {
    if (element) {
        element.classList.add('loading');
        const originalContent = element.innerHTML;
        element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        element.dataset.originalContent = originalContent;
    }
}

function hideLoading(element) {
    if (element && element.dataset.originalContent) {
        element.classList.remove('loading');
        element.innerHTML = element.dataset.originalContent;
        delete element.dataset.originalContent;
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// دوال مساعدة للأجهزة
function isMobile() {
    return window.innerWidth <= 768;
}

function isTablet() {
    return window.innerWidth > 768 && window.innerWidth <= 1024;
}

function isDesktop() {
    return window.innerWidth > 1024;
}

// دوال مساعدة للصوت
function playNotificationSound() {
    try {
        const audio = new Audio('/static/sounds/notification.mp3');
        audio.volume = 0.5;
        audio.play().catch(error => {
            console.log('لا يمكن تشغيل الصوت:', error);
        });
    } catch (error) {
        console.log('خطأ في تشغيل الصوت:', error);
    }
}

// دوال مساعدة للنسخ
async function copyToClipboard(text) {
    try {
        if (navigator.clipboard) {
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // طريقة بديلة للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return true;
        }
    } catch (error) {
        console.error('خطأ في النسخ:', error);
        return false;
    }
}

// تصدير الدوال للاستخدام العام
window.TrustMarket = {
    showToast,
    showNotification,
    formatDate,
    formatTime,
    formatDateTime,
    getRelativeTime,
    escapeHtml,
    truncateText,
    isValidEmail,
    isValidPassword,
    setLocalStorage,
    getLocalStorage,
    removeLocalStorage,
    makeRequest,
    showLoading,
    hideLoading,
    debounce,
    throttle,
    isMobile,
    isTablet,
    isDesktop,
    playNotificationSound,
    copyToClipboard
};
