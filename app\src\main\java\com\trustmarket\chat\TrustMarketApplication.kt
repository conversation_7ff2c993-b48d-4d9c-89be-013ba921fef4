package com.trustmarket.chat

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.core.app.NotificationManagerCompat
import com.google.firebase.FirebaseApp
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.HiltAndroidApp

/**
 * Application class for Trust Market Chat
 * Initializes Firebase, Hilt, and notification channels
 */
@HiltAndroidApp
class TrustMarketApplication : Application() {

    companion object {
        const val NOTIFICATION_CHANNEL_ID = "trust_market_chat_channel"
        const val NOTIFICATION_CHANNEL_NAME = "Trust Market Chat"
        const val NOTIFICATION_CHANNEL_DESCRIPTION = "Notifications for Trust Market Chat"
        
        // Firebase Database reference
        lateinit var database: FirebaseDatabase
            private set
    }

    override fun onCreate() {
        super.onCreate()
        
        // Initialize Firebase
        initializeFirebase()
        
        // Create notification channels
        createNotificationChannels()
        
        // Initialize Firebase Messaging
        initializeFirebaseMessaging()
    }

    /**
     * Initialize Firebase services
     */
    private fun initializeFirebase() {
        try {
            // Initialize Firebase App
            FirebaseApp.initializeApp(this)
            
            // Get Firebase Database instance
            database = FirebaseDatabase.getInstance()
            
            // Enable offline persistence
            database.setPersistenceEnabled(true)
            
            // Keep data synced for important paths
            database.getReference("rooms").keepSynced(true)
            database.getReference("users").keepSynced(true)
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * Create notification channels for Android O and above
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Main notification channel
            val mainChannel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                NOTIFICATION_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = NOTIFICATION_CHANNEL_DESCRIPTION
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
            
            // Private chat notification channel
            val privateChatChannel = NotificationChannel(
                "private_chat_channel",
                "Private Chat Messages",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for private chat messages"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
            
            // Group chat notification channel
            val groupChatChannel = NotificationChannel(
                "group_chat_channel",
                "Group Chat Messages",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Notifications for group chat messages"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
            
            // Lucky wheel notification channel
            val luckyWheelChannel = NotificationChannel(
                "lucky_wheel_channel",
                "Lucky Wheel Rewards",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Notifications for lucky wheel rewards"
                enableLights(true)
                enableVibration(false)
                setShowBadge(false)
            }
            
            // Admin notification channel
            val adminChannel = NotificationChannel(
                "admin_channel",
                "Admin Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for admin activities"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
            
            // Create all channels
            notificationManager.createNotificationChannels(
                listOf(
                    mainChannel,
                    privateChatChannel,
                    groupChatChannel,
                    luckyWheelChannel,
                    adminChannel
                )
            )
        }
    }

    /**
     * Initialize Firebase Messaging
     */
    private fun initializeFirebaseMessaging() {
        try {
            // Get FCM token
            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    return@addOnCompleteListener
                }
                
                // Get new FCM registration token
                val token = task.result
                
                // Log token for debugging
                android.util.Log.d("FCM", "FCM Registration Token: $token")
                
                // TODO: Send token to server
                // sendTokenToServer(token)
            }
            
            // Subscribe to general topic
            FirebaseMessaging.getInstance().subscribeToTopic("general")
                .addOnCompleteListener { task ->
                    var msg = "Subscribed to general topic"
                    if (!task.isSuccessful) {
                        msg = "Failed to subscribe to general topic"
                    }
                    android.util.Log.d("FCM", msg)
                }
                
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * Check if notifications are enabled
     */
    fun areNotificationsEnabled(): Boolean {
        return NotificationManagerCompat.from(this).areNotificationsEnabled()
    }

    /**
     * Get notification manager
     */
    fun getNotificationManager(): NotificationManager {
        return getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    }
}
