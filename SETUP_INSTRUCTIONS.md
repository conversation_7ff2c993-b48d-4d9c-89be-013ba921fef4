# تعليمات إعداد وتشغيل تطبيق Trust Market Chat

## 🚀 المتطلبات الأساسية

### 1. البرامج المطلوبة
- **Android Studio** (أحدث إصدار)
- **JDK 11** أو أعلى
- **Git** لاستنساخ المشروع
- **جهاز Android** أو **محاكي** للاختبار

### 2. حساب Firebase
- حساب Google للوصول إلى Firebase Console
- مشروع Firebase جديد أو موجود

## 📁 هيكل المشروع

```
Trust Market Chat/
├── app/
│   ├── src/main/java/com/trustmarket/chat/
│   │   ├── data/
│   │   │   ├── model/          # نماذج البيانات
│   │   │   └── repository/     # طبقة البيانات
│   │   ├── di/                 # Dependency Injection
│   │   ├── presentation/       # طبقة العرض
│   │   │   ├── auth/          # شاشات المصادقة
│   │   │   ├── home/          # الشاشة الرئيسية
│   │   │   ├── chat/          # شاشات الدردشة
│   │   │   ├── privatechat/   # الدردشة الخاصة
│   │   │   ├── luckywheel/    # عجلة الحظ
│   │   │   ├── rewardcode/    # أكواد المكافآت
│   │   │   ├── settings/      # الإعدادات
│   │   │   ├── components/    # مكونات مشتركة
│   │   │   ├── navigation/    # التنقل
│   │   │   └── theme/         # تصميم التطبيق
│   │   └── services/          # الخدمات الخلفية
│   └── src/main/res/          # الموارد
├── build.gradle               # إعدادات المشروع
├── google-services.json       # إعدادات Firebase
└── README_ANDROID.md          # وثائق المشروع
```

## 🔧 خطوات الإعداد

### الخطوة 1: إعداد Firebase

1. **إنشاء مشروع Firebase:**
   - اذهب إلى [Firebase Console](https://console.firebase.google.com/)
   - انقر على "إنشاء مشروع" أو استخدم المشروع الموجود
   - اتبع خطوات الإعداد

2. **إضافة تطبيق Android:**
   - انقر على أيقونة Android
   - أدخل اسم الحزمة: `com.trustmarket.chat`
   - أدخل اسم التطبيق: `Trust Market Chat`
   - حمل ملف `google-services.json`

3. **تفعيل الخدمات المطلوبة:**
   ```
   ✅ Authentication (Google Sign-In)
   ✅ Realtime Database
   ✅ Cloud Storage
   ✅ Cloud Messaging (FCM)
   ✅ Crashlytics
   ```

4. **إعداد Authentication:**
   - اذهب إلى Authentication > Sign-in method
   - فعل Google Sign-In
   - أضف SHA-1 fingerprint للتطبيق

5. **إعداد Realtime Database:**
   - اذهب إلى Realtime Database
   - إنشاء قاعدة بيانات في وضع الاختبار
   - اختر المنطقة الأقرب

6. **إعداد Cloud Storage:**
   - اذهب إلى Storage
   - إنشاء bucket جديد
   - اختر المنطقة الأقرب

### الخطوة 2: إعداد المشروع

1. **استنساخ المشروع:**
   ```bash
   git clone <repository-url>
   cd trust-market-chat-android
   ```

2. **إضافة ملف Firebase:**
   - ضع ملف `google-services.json` في مجلد `app/`
   - تأكد من أن الملف في المكان الصحيح

3. **فتح المشروع:**
   - افتح Android Studio
   - اختر "Open an existing project"
   - اختر مجلد المشروع

4. **مزامنة المشروع:**
   - انتظر حتى يتم تحميل جميع التبعيات
   - انقر على "Sync Now" إذا ظهرت الرسالة

### الخطوة 3: إعداد التوقيع

1. **إنشاء Keystore:**
   ```bash
   keytool -genkey -v -keystore release-key.keystore -alias release -keyalg RSA -keysize 2048 -validity 10000
   ```

2. **الحصول على SHA-1:**
   ```bash
   keytool -list -v -keystore release-key.keystore -alias release
   ```

3. **إضافة SHA-1 إلى Firebase:**
   - اذهب إلى Project Settings في Firebase
   - أضف SHA-1 fingerprint

## ▶️ تشغيل التطبيق

### 1. تشغيل على المحاكي
```bash
# إنشاء محاكي جديد
avdmanager create avd -n TestDevice -k "system-images;android-30;google_apis;x86_64"

# تشغيل المحاكي
emulator -avd TestDevice

# تشغيل التطبيق
./gradlew installDebug
```

### 2. تشغيل على جهاز حقيقي
1. فعل "Developer Options" على الجهاز
2. فعل "USB Debugging"
3. وصل الجهاز بالكمبيوتر
4. اختر الجهاز في Android Studio
5. انقر على "Run"

## 🧪 الاختبار

### اختبار الميزات الأساسية:

1. **المصادقة:**
   - تسجيل الدخول بـ Google
   - تسجيل الخروج

2. **الدردشة:**
   - إنشاء غرفة جديدة
   - إرسال رسائل
   - الانضمام لغرف موجودة

3. **عجلة الحظ:**
   - دوران العجلة
   - ربح النقاط

4. **أكواد المكافآت:**
   - استخدام الأكواد التجريبية:
     - `WELCOME2024` - 100 نقطة
     - `TESTCODE` - 25 نقطة
     - `POINTS50` - 50 نقطة

5. **الإعدادات:**
   - تعديل الملف الشخصي
   - تغيير إعدادات الإشعارات

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **خطأ في google-services.json:**
   ```
   الحل: تأكد من وضع الملف في app/ وليس في المجلد الجذر
   ```

2. **فشل في تسجيل الدخول:**
   ```
   الحل: تأكد من إضافة SHA-1 fingerprint في Firebase
   ```

3. **مشاكل في البناء:**
   ```bash
   # تنظيف المشروع
   ./gradlew clean
   
   # إعادة البناء
   ./gradlew build
   ```

4. **مشاكل في التبعيات:**
   ```bash
   # تحديث التبعيات
   ./gradlew --refresh-dependencies
   ```

## 📱 بناء APK للإنتاج

```bash
# بناء APK للإصدار
./gradlew assembleRelease

# بناء AAB للنشر على Google Play
./gradlew bundleRelease
```

## 🚀 النشر

### Google Play Store:
1. إنشاء حساب مطور
2. رفع AAB file
3. إكمال معلومات التطبيق
4. نشر التطبيق

### Firebase App Distribution:
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# رفع APK
firebase appdistribution:distribute app-release.apk --app YOUR_APP_ID
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من [الوثائق](README_ANDROID.md)
2. ابحث في Issues على GitHub
3. أنشئ Issue جديد مع تفاصيل المشكلة

## 🎉 تهانينا!

تطبيق Trust Market Chat جاهز للاستخدام! 🚀

### الميزات المتاحة:
- ✅ دردشة جماعية وخاصة
- ✅ عجلة الحظ مع الجوائز
- ✅ أكواد المكافآت
- ✅ إعدادات شاملة
- ✅ إشعارات ذكية
- ✅ واجهة عربية جميلة

استمتع بالتطبيق! 🎊
