package com.trustmarket.chat.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Notification data model
 */
@Parcelize
data class Notification(
    val id: String = "",
    val title: String = "",
    val body: String = "",
    val imageUrl: String? = null,
    val type: NotificationType = NotificationType.MESSAGE,
    val data: Map<String, String> = emptyMap(),
    val userId: String = "",
    val senderId: String? = null,
    val senderName: String? = null,
    val roomId: String? = null,
    val roomName: String? = null,
    val messageId: String? = null,
    val timestamp: Long = 0L,
    val isRead: Boolean = false,
    val readAt: Long = 0L,
    val priority: NotificationPriority = NotificationPriority.NORMAL,
    val actionButtons: List<NotificationAction> = emptyList(),
    val sound: String = "default",
    val vibrationPattern: List<Long> = emptyList(),
    val color: String? = null,
    val channelId: String = "default_channel",
    val groupKey: String? = null,
    val isGroupSummary: Boolean = false,
    val autoCancel: Boolean = true,
    val ongoing: Boolean = false,
    val expiresAt: Long = 0L
) : Parcelable {
    
    /**
     * Check if notification is expired
     */
    fun isExpired(): Boolean {
        return expiresAt > 0 && System.currentTimeMillis() > expiresAt
    }
    
    /**
     * Get formatted timestamp
     */
    fun getFormattedTime(): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        val minutes = diff / (1000 * 60)
        val hours = minutes / 60
        val days = hours / 24
        
        return when {
            minutes < 1 -> "الآن"
            minutes < 60 -> "منذ ${minutes} دقيقة"
            hours < 24 -> "منذ ${hours} ساعة"
            days < 7 -> "منذ ${days} يوم"
            else -> {
                val date = java.util.Date(timestamp)
                java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault()).format(date)
            }
        }
    }
    
    /**
     * Get notification icon based on type
     */
    fun getIconResource(): String {
        return when (type) {
            NotificationType.MESSAGE -> "ic_message"
            NotificationType.PRIVATE_MESSAGE -> "ic_private_message"
            NotificationType.MENTION -> "ic_mention"
            NotificationType.REPLY -> "ic_reply"
            NotificationType.LIKE -> "ic_like"
            NotificationType.ROOM_INVITE -> "ic_room_invite"
            NotificationType.ROOM_UPDATE -> "ic_room_update"
            NotificationType.USER_JOIN -> "ic_user_join"
            NotificationType.USER_LEAVE -> "ic_user_leave"
            NotificationType.ADMIN_ACTION -> "ic_admin"
            NotificationType.LUCKY_WHEEL -> "ic_lucky_wheel"
            NotificationType.REWARD_CODE -> "ic_reward"
            NotificationType.SYSTEM -> "ic_system"
            NotificationType.UPDATE -> "ic_update"
            NotificationType.PROMOTION -> "ic_promotion"
        }
    }
}

/**
 * Notification types
 */
enum class NotificationType {
    MESSAGE,
    PRIVATE_MESSAGE,
    MENTION,
    REPLY,
    LIKE,
    ROOM_INVITE,
    ROOM_UPDATE,
    USER_JOIN,
    USER_LEAVE,
    ADMIN_ACTION,
    LUCKY_WHEEL,
    REWARD_CODE,
    SYSTEM,
    UPDATE,
    PROMOTION
}

/**
 * Notification priority
 */
enum class NotificationPriority {
    MIN,
    LOW,
    NORMAL,
    HIGH,
    MAX
}

/**
 * Notification action button
 */
@Parcelize
data class NotificationAction(
    val id: String = "",
    val title: String = "",
    val icon: String? = null,
    val action: String = "",
    val data: Map<String, String> = emptyMap(),
    val isDestructive: Boolean = false,
    val requiresAuth: Boolean = false
) : Parcelable

/**
 * Lucky wheel prize data model
 */
@Parcelize
data class LuckyWheelPrize(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val imageUrl: String? = null,
    val points: Int = 0,
    val probability: Float = 0f, // 0.0 to 1.0
    val isActive: Boolean = true,
    val color: String = "#FFFFFF",
    val textColor: String = "#000000",
    val createdAt: Long = 0L,
    val updatedAt: Long = 0L,
    val expiresAt: Long = 0L,
    val maxWins: Int = -1, // -1 for unlimited
    val currentWins: Int = 0,
    val requiredLevel: Int = 0,
    val isSpecial: Boolean = false,
    val specialConditions: List<String> = emptyList()
) : Parcelable {
    
    /**
     * Check if prize is available
     */
    fun isAvailable(): Boolean {
        return isActive && 
               (expiresAt == 0L || System.currentTimeMillis() < expiresAt) &&
               (maxWins == -1 || currentWins < maxWins)
    }
    
    /**
     * Check if user can win this prize
     */
    fun canUserWin(userLevel: Int): Boolean {
        return isAvailable() && userLevel >= requiredLevel
    }
}

/**
 * Reward code data model
 */
@Parcelize
data class RewardCode(
    val id: String = "",
    val code: String = "",
    val name: String = "",
    val description: String = "",
    val points: Int = 0,
    val type: RewardCodeType = RewardCodeType.POINTS,
    val isActive: Boolean = true,
    val createdAt: Long = 0L,
    val expiresAt: Long = 0L,
    val maxUses: Int = -1, // -1 for unlimited
    val currentUses: Int = 0,
    val usedBy: List<String> = emptyList(),
    val createdBy: String = "",
    val targetAudience: List<String> = emptyList(), // empty for all users
    val requiredLevel: Int = 0,
    val isOneTimeUse: Boolean = true,
    val cooldownHours: Int = 0
) : Parcelable {
    
    /**
     * Check if code is valid
     */
    fun isValid(): Boolean {
        return isActive && 
               (expiresAt == 0L || System.currentTimeMillis() < expiresAt) &&
               (maxUses == -1 || currentUses < maxUses)
    }
    
    /**
     * Check if user can use this code
     */
    fun canUserUse(userId: String, userLevel: Int): Boolean {
        return isValid() && 
               userLevel >= requiredLevel &&
               (targetAudience.isEmpty() || targetAudience.contains(userId)) &&
               (!isOneTimeUse || !usedBy.contains(userId))
    }
}

/**
 * Reward code types
 */
enum class RewardCodeType {
    POINTS,
    LUCKY_WHEEL_SPIN,
    PREMIUM_FEATURE,
    SPECIAL_BADGE,
    CUSTOM_REWARD
}
