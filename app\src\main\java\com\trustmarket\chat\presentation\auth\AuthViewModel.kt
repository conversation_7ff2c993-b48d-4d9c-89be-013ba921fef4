package com.trustmarket.chat.presentation.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.firebase.auth.FirebaseUser
import com.trustmarket.chat.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for handling authentication operations
 */
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    init {
        checkAuthState()
    }

    /**
     * Check current authentication state
     */
    fun checkAuthState() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                if (authRepository.isAuthenticated()) {
                    val user = authRepository.getCurrentFirebaseUser()
                    if (user != null) {
                        _authState.value = AuthState.Authenticated(user)
                    } else {
                        _authState.value = AuthState.Unauthenticated
                    }
                } else {
                    _authState.value = AuthState.Unauthenticated
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error(e)
                _errorMessage.value = e.message
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Sign in with Google
     */
    fun signInWithGoogle(account: GoogleSignInAccount) {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null
            
            try {
                val result = authRepository.signInWithGoogle(account)
                result.fold(
                    onSuccess = { user ->
                        _authState.value = AuthState.Authenticated(user)
                    },
                    onFailure = { exception ->
                        _authState.value = AuthState.Error(exception)
                        _errorMessage.value = exception.message ?: "فشل في تسجيل الدخول"
                    }
                )
            } catch (e: Exception) {
                _authState.value = AuthState.Error(e)
                _errorMessage.value = e.message ?: "حدث خطأ غير متوقع"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Sign out
     */
    fun signOut() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val result = authRepository.signOut()
                result.fold(
                    onSuccess = {
                        _authState.value = AuthState.Unauthenticated
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "فشل في تسجيل الخروج"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ أثناء تسجيل الخروج"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Get current user ID
     */
    fun getCurrentUserId(): String? {
        return authRepository.getCurrentUserId()
    }

    /**
     * Get current Firebase user
     */
    fun getCurrentUser(): FirebaseUser? {
        return authRepository.getCurrentFirebaseUser()
    }

    /**
     * Update FCM token
     */
    fun updateFcmToken(token: String) {
        viewModelScope.launch {
            try {
                authRepository.updateFcmToken(token)
            } catch (e: Exception) {
                // Handle error silently
            }
        }
    }

    /**
     * Delete user account
     */
    fun deleteAccount() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val result = authRepository.deleteAccount()
                result.fold(
                    onSuccess = {
                        _authState.value = AuthState.Unauthenticated
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "فشل في حذف الحساب"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ أثناء حذف الحساب"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Authentication states
     */
    sealed class AuthState {
        object Loading : AuthState()
        object Unauthenticated : AuthState()
        data class Authenticated(val user: FirebaseUser) : AuthState()
        data class Error(val exception: Throwable) : AuthState()
    }
}
