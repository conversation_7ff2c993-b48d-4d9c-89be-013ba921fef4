/* الألوان والمتغيرات */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --bg-dark: #121212;
    --bg-secondary: #1e1e1e;
    --text-light: #ffffff;
    --text-muted: #6c757d;
}

/* الإعدادات العامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

.dark-theme {
    background-color: var(--bg-dark);
    color: var(--text-light);
}

/* التنقل */
.navbar {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    margin-left: 8px;
    color: var(--primary-color);
}

/* المحتوى الرئيسي */
.main-content {
    margin-top: 76px;
    min-height: calc(100vh - 76px);
    padding-bottom: 80px;
}

/* التنقل السفلي للجوال */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--bg-secondary);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 10px 0;
    z-index: 1000;
}

.bottom-nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-muted);
    transition: color 0.3s;
    padding: 5px 10px;
}

.bottom-nav-item:hover,
.bottom-nav-item.active {
    color: var(--primary-color);
}

.bottom-nav-item i {
    font-size: 1.2rem;
    margin-bottom: 2px;
}

.bottom-nav-item span {
    font-size: 0.8rem;
}

/* صفحة تسجيل الدخول */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-secondary) 100%);
}

.login-card {
    background-color: var(--bg-secondary);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.login-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.login-title {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    border-radius: 8px;
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    color: var(--text-light);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.form-label i {
    margin-left: 5px;
    color: var(--primary-color);
}

/* صفحة الدردشة */
.chat-container {
    height: calc(100vh - 76px);
}

.rooms-sidebar {
    height: 100%;
    background-color: var(--bg-secondary);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h5 {
    margin: 0;
    color: var(--text-light);
}

.sidebar-header i {
    margin-left: 8px;
    color: var(--primary-color);
}

.rooms-list {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.room-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid transparent;
}

.room-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
}

.room-info h6 {
    margin: 0;
    color: var(--text-light);
    font-size: 0.9rem;
}

.room-description {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.room-stats .badge {
    font-size: 0.7rem;
}

/* منطقة الدردشة */
.chat-area {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-dark);
}

.chat-header {
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.room-info h6 {
    margin: 0;
    color: var(--text-light);
}

.messages-area {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: linear-gradient(to bottom, var(--bg-dark), var(--bg-secondary));
}

.welcome-message {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.message {
    margin-bottom: 1rem;
    display: flex;
}

.message.own-message {
    justify-content: flex-end;
}

.message.other-message {
    justify-content: flex-start;
}

.message.system-message {
    justify-content: center;
}

.message-content {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 18px;
    position: relative;
}

.own-message .message-content {
    background-color: var(--primary-color);
    color: white;
    border-bottom-left-radius: 4px;
}

.other-message .message-content {
    background-color: var(--bg-secondary);
    color: var(--text-light);
    border-bottom-right-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.system-message .message-content {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-muted);
    border-radius: 20px;
    text-align: center;
    font-size: 0.9rem;
    max-width: 80%;
}

.message-author {
    font-size: 0.8rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.message-text {
    word-wrap: break-word;
    line-height: 1.4;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.7;
    margin-top: 0.25rem;
    text-align: left;
}

/* مؤشر الكتابة */
.typing-indicator {
    padding: 0.5rem 1rem;
    background-color: var(--bg-secondary);
}

.typing-dots span {
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        opacity: 0.3;
    }
    30% {
        opacity: 1;
    }
}

/* منطقة إدخال الرسائل */
.message-input-area {
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.message-input-area .form-control {
    border-radius: 25px;
    padding: 0.75rem 1rem;
}

.message-input-area .btn {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .main-content {
        margin-top: 56px;
        min-height: calc(100vh - 56px);
        padding-bottom: 120px;
    }
    
    .chat-container {
        height: calc(100vh - 56px);
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .login-card {
        margin: 1rem;
        padding: 1.5rem;
    }
}

/* تحسينات إضافية */
.btn {
    border-radius: 8px;
    transition: all 0.3s;
}

.btn:hover {
    transform: translateY(-1px);
}

.badge {
    border-radius: 12px;
}

.modal-content {
    background-color: var(--bg-secondary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
    background-color: var(--bg-secondary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-item {
    color: var(--text-light);
}

.dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* تحسينات الإمكانية */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
