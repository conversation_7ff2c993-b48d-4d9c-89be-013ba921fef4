#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق الدردشة - Trust Market
تطبيق دردشة آمن للبيع والشراء
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from flask_socketio import SocketIO, emit, join_room, leave_room, rooms
from flask_cors import CORS
import firebase_admin
from firebase_admin import credentials, firestore, auth as firebase_auth
import json
import os
from datetime import datetime
import uuid
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
CORS(app)

# إعداد SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='eventlet')

# إعداد Firebase
firebase_config = {
    "type": "service_account",
    "project_id": "toika-369",
    "private_key_id": os.getenv('FIREBASE_PRIVATE_KEY_ID'),
    "private_key": os.getenv('FIREBASE_PRIVATE_KEY', '').replace('\\n', '\n'),
    "client_email": os.getenv('FIREBASE_CLIENT_EMAIL'),
    "client_id": os.getenv('FIREBASE_CLIENT_ID'),
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    "client_x509_cert_url": os.getenv('FIREBASE_CLIENT_CERT_URL')
}

# تهيئة Firebase
try:
    cred = credentials.Certificate(firebase_config)
    firebase_admin.initialize_app(cred)
    db = firestore.client()
    print("✅ تم تهيئة Firebase بنجاح")
except Exception as e:
    print(f"❌ خطأ في تهيئة Firebase: {e}")
    db = None

# متغيرات عامة
connected_users = {}
active_rooms = {}

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/login')
def login():
    """صفحة تسجيل الدخول"""
    return render_template('login.html')

@app.route('/chat')
def chat():
    """صفحة الدردشة الرئيسية"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('chat.html')

@app.route('/private-chats')
def private_chats():
    """صفحة الدردشات الخاصة"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('private_chats.html')

@app.route('/settings')
def settings():
    """صفحة الإعدادات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('settings.html')

# API Routes
@app.route('/api/auth/login', methods=['POST'])
def api_login():
    """تسجيل الدخول"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        # هنا يمكنك إضافة منطق التحقق من المستخدم
        # للتبسيط، سنقوم بإنشاء جلسة مؤقتة
        user_id = str(uuid.uuid4())
        session['user_id'] = user_id
        session['email'] = email
        session['name'] = email.split('@')[0]  # استخدام الجزء الأول من البريد كاسم
        
        return jsonify({
            'success': True,
            'user': {
                'uid': user_id,
                'email': email,
                'name': session['name']
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/auth/logout', methods=['POST'])
def api_logout():
    """تسجيل الخروج"""
    session.clear()
    return jsonify({'success': True})

@app.route('/api/rooms', methods=['GET'])
def api_get_rooms():
    """الحصول على قائمة الغرف"""
    try:
        # غرف افتراضية
        rooms = [
            {
                'id': 'arab-group',
                'name': 'كروب عرب',
                'description': 'مجموعة للمتحدثين بالعربية',
                'isPublic': True,
                'memberCount': len([u for u in connected_users.values() if u.get('room') == 'arab-group'])
            },
            {
                'id': 'kurdish-group',
                'name': 'كروب كردي',
                'description': 'مجموعة للمتحدثين بالكردية',
                'isPublic': True,
                'memberCount': len([u for u in connected_users.values() if u.get('room') == 'kurdish-group'])
            }
        ]
        return jsonify({'success': True, 'rooms': rooms})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

# Socket Events
@socketio.on('connect')
def on_connect():
    """عند الاتصال"""
    print(f"🔗 مستخدم متصل: {request.sid}")
    
@socketio.on('disconnect')
def on_disconnect():
    """عند قطع الاتصال"""
    print(f"❌ مستخدم منقطع: {request.sid}")
    if request.sid in connected_users:
        user = connected_users[request.sid]
        if user.get('room'):
            leave_room(user['room'])
        del connected_users[request.sid]

@socketio.on('join_room')
def on_join_room(data):
    """الانضمام إلى غرفة"""
    try:
        room_id = data.get('room_id')
        user_info = data.get('user')
        
        if not room_id or not user_info:
            emit('error', {'message': 'بيانات غير صحيحة'})
            return
            
        # إضافة المستخدم إلى الغرفة
        join_room(room_id)
        connected_users[request.sid] = {
            'user_id': user_info.get('uid'),
            'name': user_info.get('name'),
            'email': user_info.get('email'),
            'room': room_id
        }
        
        # إشعار المستخدمين الآخرين
        emit('user_joined', {
            'user': user_info,
            'message': f"{user_info.get('name')} انضم إلى الدردشة"
        }, room=room_id)
        
        print(f"👤 {user_info.get('name')} انضم إلى الغرفة: {room_id}")
        
    except Exception as e:
        emit('error', {'message': f'خطأ في الانضمام: {str(e)}'})

@socketio.on('leave_room')
def on_leave_room(data):
    """مغادرة الغرفة"""
    try:
        room_id = data.get('room_id')
        user_info = data.get('user')
        
        leave_room(room_id)
        
        if request.sid in connected_users:
            connected_users[request.sid]['room'] = None
            
        # إشعار المستخدمين الآخرين
        emit('user_left', {
            'user': user_info,
            'message': f"{user_info.get('name')} غادر الدردشة"
        }, room=room_id)
        
        print(f"👋 {user_info.get('name')} غادر الغرفة: {room_id}")
        
    except Exception as e:
        emit('error', {'message': f'خطأ في المغادرة: {str(e)}'})

@socketio.on('send_message')
def on_send_message(data):
    """إرسال رسالة"""
    try:
        room_id = data.get('room_id')
        message_content = data.get('content')
        user_info = data.get('user')
        
        if not all([room_id, message_content, user_info]):
            emit('error', {'message': 'بيانات الرسالة غير مكتملة'})
            return
            
        # إنشاء الرسالة
        message = {
            'id': str(uuid.uuid4()),
            'content': message_content,
            'author': {
                'uid': user_info.get('uid'),
                'name': user_info.get('name'),
                'email': user_info.get('email')
            },
            'createdAt': datetime.now().isoformat(),
            'room_id': room_id
        }
        
        # إرسال الرسالة لجميع المستخدمين في الغرفة
        emit('new_message', message, room=room_id)
        
        print(f"💬 رسالة جديدة من {user_info.get('name')} في {room_id}: {message_content}")
        
    except Exception as e:
        emit('error', {'message': f'خطأ في إرسال الرسالة: {str(e)}'})

@socketio.on('typing_start')
def on_typing_start(data):
    """بدء الكتابة"""
    try:
        room_id = data.get('room_id')
        user_info = data.get('user')
        
        emit('user_typing', {
            'user': user_info,
            'typing': True
        }, room=room_id, include_self=False)
        
    except Exception as e:
        pass

@socketio.on('typing_stop')
def on_typing_stop(data):
    """توقف الكتابة"""
    try:
        room_id = data.get('room_id')
        user_info = data.get('user')
        
        emit('user_typing', {
            'user': user_info,
            'typing': False
        }, room=room_id, include_self=False)
        
    except Exception as e:
        pass

if __name__ == '__main__':
    print("🚀 بدء تشغيل خادم الدردشة...")
    print("📱 Trust Market - منصة آمنة للبيع والشراء")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
