package com.trustmarket.chat.presentation.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import com.trustmarket.chat.R

/**
 * Bottom navigation bar for the app
 */
@Composable
fun BottomNavigationBar(
    navController: NavController,
    currentRoute: String?
) {
    NavigationBar {
        // Home
        NavigationBarItem(
            icon = { 
                Icon(
                    imageVector = Icons.Default.Home,
                    contentDescription = stringResource(id = R.string.home)
                )
            },
            label = { Text(stringResource(id = R.string.home)) },
            selected = currentRoute == "home",
            onClick = {
                if (currentRoute != "home") {
                    navController.navigate("home") {
                        popUpTo("home") { inclusive = true }
                    }
                }
            }
        )

        // Private Chats
        NavigationBarItem(
            icon = { 
                Icon(
                    imageVector = Icons.Default.Chat,
                    contentDescription = stringResource(id = R.string.private_chats)
                )
            },
            label = { Text(stringResource(id = R.string.private_chats)) },
            selected = currentRoute == "private_chats",
            onClick = {
                if (currentRoute != "private_chats") {
                    navController.navigate("private_chats")
                }
            }
        )

        // Lucky Wheel
        NavigationBarItem(
            icon = { 
                Icon(
                    imageVector = Icons.Default.Casino,
                    contentDescription = stringResource(id = R.string.lucky_wheel)
                )
            },
            label = { Text(stringResource(id = R.string.lucky_wheel)) },
            selected = currentRoute == "lucky_wheel",
            onClick = {
                if (currentRoute != "lucky_wheel") {
                    navController.navigate("lucky_wheel")
                }
            }
        )

        // Notifications
        NavigationBarItem(
            icon = { 
                Icon(
                    imageVector = Icons.Default.Notifications,
                    contentDescription = stringResource(id = R.string.notifications)
                )
            },
            label = { Text(stringResource(id = R.string.notifications)) },
            selected = currentRoute == "notifications",
            onClick = {
                if (currentRoute != "notifications") {
                    navController.navigate("notifications")
                }
            }
        )

        // Settings
        NavigationBarItem(
            icon = { 
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = stringResource(id = R.string.settings)
                )
            },
            label = { Text(stringResource(id = R.string.settings)) },
            selected = currentRoute == "settings",
            onClick = {
                if (currentRoute != "settings") {
                    navController.navigate("settings")
                }
            }
        )
    }
}
