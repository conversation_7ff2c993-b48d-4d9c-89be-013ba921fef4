package com.trustmarket.chat.presentation.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseUser
import com.trustmarket.chat.data.model.NotificationSettings
import com.trustmarket.chat.data.model.PrivacySettings
import com.trustmarket.chat.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for Settings screen
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _currentUser = MutableStateFlow<FirebaseUser?>(null)
    val currentUser: StateFlow<FirebaseUser?> = _currentUser.asStateFlow()

    private val _notificationSettings = MutableStateFlow(NotificationSettings())
    val notificationSettings: StateFlow<NotificationSettings> = _notificationSettings.asStateFlow()

    private val _privacySettings = MutableStateFlow(PrivacySettings())
    val privacySettings: StateFlow<PrivacySettings> = _privacySettings.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()

    init {
        loadCurrentUser()
    }

    /**
     * Load current user
     */
    private fun loadCurrentUser() {
        _currentUser.value = authRepository.getCurrentFirebaseUser()
    }

    /**
     * Load user settings from Firebase
     */
    fun loadUserSettings() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val currentUser = authRepository.getCurrentFirebaseUser()
                if (currentUser != null) {
                    // TODO: Load settings from Firebase
                    // For now, use default settings
                    _notificationSettings.value = NotificationSettings(
                        enableNotifications = true,
                        enablePrivateMessageNotifications = true,
                        enableGroupMessageNotifications = true,
                        enableLuckyWheelNotifications = true,
                        enableAdminNotifications = true,
                        vibrationEnabled = true,
                        quietHoursEnabled = false,
                        quietHoursStart = "22:00",
                        quietHoursEnd = "08:00"
                    )
                    
                    _privacySettings.value = PrivacySettings(
                        showOnlineStatus = true,
                        showLastSeen = true,
                        allowPrivateMessages = true,
                        showProfilePhoto = true,
                        showPhoneNumber = false,
                        showEmail = false,
                        blockedUsers = emptyList()
                    )
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحميل الإعدادات"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Update notification setting
     */
    fun updateNotificationSetting(setting: String, value: Any) {
        viewModelScope.launch {
            try {
                val currentSettings = _notificationSettings.value
                val updatedSettings = when (setting) {
                    "enableNotifications" -> currentSettings.copy(enableNotifications = value as Boolean)
                    "enablePrivateMessageNotifications" -> currentSettings.copy(enablePrivateMessageNotifications = value as Boolean)
                    "enableGroupMessageNotifications" -> currentSettings.copy(enableGroupMessageNotifications = value as Boolean)
                    "enableLuckyWheelNotifications" -> currentSettings.copy(enableLuckyWheelNotifications = value as Boolean)
                    "enableAdminNotifications" -> currentSettings.copy(enableAdminNotifications = value as Boolean)
                    "vibrationEnabled" -> currentSettings.copy(vibrationEnabled = value as Boolean)
                    "quietHoursEnabled" -> currentSettings.copy(quietHoursEnabled = value as Boolean)
                    "quietHoursStart" -> currentSettings.copy(quietHoursStart = value as String)
                    "quietHoursEnd" -> currentSettings.copy(quietHoursEnd = value as String)
                    "notificationSound" -> currentSettings.copy(notificationSound = value as String)
                    else -> currentSettings
                }
                
                _notificationSettings.value = updatedSettings
                
                // TODO: Save to Firebase
                saveNotificationSettings(updatedSettings)
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحديث إعدادات الإشعارات"
            }
        }
    }

    /**
     * Update privacy setting
     */
    fun updatePrivacySetting(setting: String, value: Any) {
        viewModelScope.launch {
            try {
                val currentSettings = _privacySettings.value
                val updatedSettings = when (setting) {
                    "showOnlineStatus" -> currentSettings.copy(showOnlineStatus = value as Boolean)
                    "showLastSeen" -> currentSettings.copy(showLastSeen = value as Boolean)
                    "allowPrivateMessages" -> currentSettings.copy(allowPrivateMessages = value as Boolean)
                    "showProfilePhoto" -> currentSettings.copy(showProfilePhoto = value as Boolean)
                    "showPhoneNumber" -> currentSettings.copy(showPhoneNumber = value as Boolean)
                    "showEmail" -> currentSettings.copy(showEmail = value as Boolean)
                    else -> currentSettings
                }
                
                _privacySettings.value = updatedSettings
                
                // TODO: Save to Firebase
                savePrivacySettings(updatedSettings)
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحديث إعدادات الخصوصية"
            }
        }
    }

    /**
     * Update user profile
     */
    fun updateProfile(name: String, bio: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val currentUser = authRepository.getCurrentFirebaseUser()
                if (currentUser != null) {
                    // TODO: Update profile in Firebase
                    // For now, just show success message
                    _successMessage.value = "تم تحديث الملف الشخصي بنجاح"
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحديث الملف الشخصي"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Update profile picture
     */
    fun updateProfilePicture(imageUri: String) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                // TODO: Upload image to Firebase Storage and update profile
                _successMessage.value = "تم تحديث الصورة الشخصية بنجاح"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحديث الصورة الشخصية"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Block user
     */
    fun blockUser(userId: String) {
        viewModelScope.launch {
            try {
                val currentSettings = _privacySettings.value
                val updatedBlockedUsers = currentSettings.blockedUsers + userId
                val updatedSettings = currentSettings.copy(blockedUsers = updatedBlockedUsers)
                
                _privacySettings.value = updatedSettings
                savePrivacySettings(updatedSettings)
                
                _successMessage.value = "تم حظر المستخدم"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في حظر المستخدم"
            }
        }
    }

    /**
     * Unblock user
     */
    fun unblockUser(userId: String) {
        viewModelScope.launch {
            try {
                val currentSettings = _privacySettings.value
                val updatedBlockedUsers = currentSettings.blockedUsers.filter { it != userId }
                val updatedSettings = currentSettings.copy(blockedUsers = updatedBlockedUsers)
                
                _privacySettings.value = updatedSettings
                savePrivacySettings(updatedSettings)
                
                _successMessage.value = "تم إلغاء حظر المستخدم"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في إلغاء حظر المستخدم"
            }
        }
    }

    /**
     * Sign out
     */
    fun signOut() {
        viewModelScope.launch {
            try {
                authRepository.signOut()
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تسجيل الخروج"
            }
        }
    }

    /**
     * Delete account
     */
    fun deleteAccount() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val result = authRepository.deleteAccount()
                result.fold(
                    onSuccess = {
                        _successMessage.value = "تم حذف الحساب بنجاح"
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "فشل في حذف الحساب"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "حدث خطأ أثناء حذف الحساب"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Save notification settings to Firebase
     */
    private suspend fun saveNotificationSettings(settings: NotificationSettings) {
        try {
            val currentUser = authRepository.getCurrentFirebaseUser()
            if (currentUser != null) {
                // TODO: Save to Firebase Database
                // firebaseDatabase.getReference("users/${currentUser.uid}/notificationSettings")
                //     .setValue(settings).await()
            }
        } catch (e: Exception) {
            throw e
        }
    }

    /**
     * Save privacy settings to Firebase
     */
    private suspend fun savePrivacySettings(settings: PrivacySettings) {
        try {
            val currentUser = authRepository.getCurrentFirebaseUser()
            if (currentUser != null) {
                // TODO: Save to Firebase Database
                // firebaseDatabase.getReference("users/${currentUser.uid}/privacySettings")
                //     .setValue(settings).await()
            }
        } catch (e: Exception) {
            throw e
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Clear success message
     */
    fun clearSuccess() {
        _successMessage.value = null
    }

    /**
     * Export user data
     */
    fun exportUserData() {
        viewModelScope.launch {
            try {
                // TODO: Export user data to file
                _successMessage.value = "تم تصدير البيانات بنجاح"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تصدير البيانات"
            }
        }
    }

    /**
     * Clear app cache
     */
    fun clearAppCache() {
        viewModelScope.launch {
            try {
                // TODO: Clear app cache
                _successMessage.value = "تم مسح ذاكرة التخزين المؤقت"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في مسح ذاكرة التخزين المؤقت"
            }
        }
    }

    /**
     * Check for app updates
     */
    fun checkForUpdates() {
        viewModelScope.launch {
            try {
                // TODO: Check for app updates
                _successMessage.value = "التطبيق محدث إلى أحدث إصدار"
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في فحص التحديثات"
            }
        }
    }

    /**
     * Get app version
     */
    fun getAppVersion(): String {
        return "1.0.0" // TODO: Get from BuildConfig
    }

    /**
     * Get app size
     */
    fun getAppSize(): String {
        return "25 MB" // TODO: Calculate actual app size
    }

    /**
     * Get cache size
     */
    fun getCacheSize(): String {
        return "12 MB" // TODO: Calculate actual cache size
    }
}
