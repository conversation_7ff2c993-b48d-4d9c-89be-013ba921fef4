// Top-level build file where you can add configuration options common to all sub-modules/sub-projects.
buildscript {
    ext {
        kotlin_version = '1.9.10'
        compose_version = '1.5.4'
        compose_bom_version = '2023.10.01'
        lifecycle_version = '2.7.0'
        navigation_version = '2.7.5'
        room_version = '2.6.0'
        hilt_version = '2.48'
        coroutines_version = '1.7.3'
        retrofit_version = '2.9.0'
        okhttp_version = '4.12.0'
        glide_version = '4.16.0'
        firebase_bom_version = '32.7.0'
    }
    
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.0'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_version"
    }
}

plugins {
    id 'com.android.application' version '8.1.4' apply false
    id 'com.android.library' version '8.1.4' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.10' apply false
    id 'com.google.gms.google-services' version '4.4.0' apply false
    id 'com.google.firebase.crashlytics' version '2.9.9' apply false
    id 'com.google.dagger.hilt.android' version '2.48' apply false
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
