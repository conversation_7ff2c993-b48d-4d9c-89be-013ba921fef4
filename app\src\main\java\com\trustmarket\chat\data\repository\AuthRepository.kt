package com.trustmarket.chat.data.repository

import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.messaging.FirebaseMessaging
import com.trustmarket.chat.data.model.User
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for handling authentication operations
 */
@Singleton
class AuthRepository @Inject constructor(
    private val firebaseAuth: FirebaseAuth,
    private val firebaseDatabase: FirebaseDatabase,
    private val firebaseMessaging: FirebaseMessaging
) {

    private val _currentUser = MutableStateFlow<FirebaseUser?>(null)
    val currentUser: Flow<FirebaseUser?> = _currentUser.asStateFlow()

    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    val authState: Flow<AuthState> = _authState.asStateFlow()

    init {
        // Listen to auth state changes
        firebaseAuth.addAuthStateListener { auth ->
            val user = auth.currentUser
            _currentUser.value = user
            _authState.value = if (user != null) {
                AuthState.Authenticated(user)
            } else {
                AuthState.Unauthenticated
            }
        }
    }

    /**
     * Sign in with Google
     */
    suspend fun signInWithGoogle(account: GoogleSignInAccount): Result<FirebaseUser> {
        return try {
            val credential = GoogleAuthProvider.getCredential(account.idToken, null)
            val authResult = firebaseAuth.signInWithCredential(credential).await()
            val user = authResult.user
            
            if (user != null) {
                // Create or update user in database
                createOrUpdateUser(user)
                Result.success(user)
            } else {
                Result.failure(Exception("Authentication failed"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Sign out
     */
    suspend fun signOut(): Result<Unit> {
        return try {
            // Update user status to offline
            getCurrentUserId()?.let { userId ->
                updateUserOnlineStatus(userId, false)
            }
            
            // Sign out from Firebase
            firebaseAuth.signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get current user ID
     */
    fun getCurrentUserId(): String? {
        return firebaseAuth.currentUser?.uid
    }

    /**
     * Get current Firebase user
     */
    fun getCurrentFirebaseUser(): FirebaseUser? {
        return firebaseAuth.currentUser
    }

    /**
     * Check if user is authenticated
     */
    fun isAuthenticated(): Boolean {
        return firebaseAuth.currentUser != null
    }

    /**
     * Create or update user in database
     */
    private suspend fun createOrUpdateUser(firebaseUser: FirebaseUser) {
        try {
            val userId = firebaseUser.uid
            val userRef = firebaseDatabase.getReference("users").child(userId)
            
            // Get FCM token
            val fcmToken = try {
                firebaseMessaging.token.await()
            } catch (e: Exception) {
                null
            }
            
            // Check if user already exists
            val snapshot = userRef.get().await()
            val currentTime = System.currentTimeMillis()
            
            if (snapshot.exists()) {
                // Update existing user
                val updates = mapOf(
                    "name" to (firebaseUser.displayName ?: ""),
                    "email" to (firebaseUser.email ?: ""),
                    "photoUrl" to firebaseUser.photoUrl?.toString(),
                    "isOnline" to true,
                    "lastSeen" to currentTime,
                    "updatedAt" to currentTime,
                    "fcmToken" to fcmToken
                )
                userRef.updateChildren(updates).await()
            } else {
                // Create new user
                val user = User(
                    uid = userId,
                    name = firebaseUser.displayName ?: "",
                    email = firebaseUser.email ?: "",
                    photoUrl = firebaseUser.photoUrl?.toString(),
                    isOnline = true,
                    lastSeen = currentTime,
                    isAdmin = isAdminEmail(firebaseUser.email),
                    createdAt = currentTime,
                    updatedAt = currentTime,
                    fcmToken = fcmToken
                )
                userRef.setValue(user).await()
            }
            
            // Subscribe to user-specific FCM topic
            fcmToken?.let {
                firebaseMessaging.subscribeToTopic("user_$userId").await()
            }
            
        } catch (e: Exception) {
            throw e
        }
    }

    /**
     * Update user online status
     */
    suspend fun updateUserOnlineStatus(userId: String, isOnline: Boolean) {
        try {
            val userRef = firebaseDatabase.getReference("users").child(userId)
            val updates = mapOf(
                "isOnline" to isOnline,
                "lastSeen" to System.currentTimeMillis()
            )
            userRef.updateChildren(updates).await()
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    /**
     * Check if email is admin email
     */
    private fun isAdminEmail(email: String?): Boolean {
        val adminEmails = listOf(
            "<EMAIL>",
            "<EMAIL>"
        )
        return adminEmails.contains(email)
    }

    /**
     * Update FCM token
     */
    suspend fun updateFcmToken(token: String) {
        try {
            val userId = getCurrentUserId() ?: return
            val userRef = firebaseDatabase.getReference("users").child(userId)
            userRef.child("fcmToken").setValue(token).await()
            
            // Subscribe to user-specific topic
            firebaseMessaging.subscribeToTopic("user_$userId").await()
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    /**
     * Delete user account
     */
    suspend fun deleteAccount(): Result<Unit> {
        return try {
            val user = firebaseAuth.currentUser
            if (user != null) {
                // Delete user data from database
                val userId = user.uid
                firebaseDatabase.getReference("users").child(userId).removeValue().await()
                
                // Delete Firebase Auth account
                user.delete().await()
                
                Result.success(Unit)
            } else {
                Result.failure(Exception("No user to delete"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Authentication states
     */
    sealed class AuthState {
        object Loading : AuthState()
        object Unauthenticated : AuthState()
        data class Authenticated(val user: FirebaseUser) : AuthState()
        data class Error(val exception: Throwable) : AuthState()
    }
}
