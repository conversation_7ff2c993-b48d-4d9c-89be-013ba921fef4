package com.trustmarket.chat.presentation.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.trustmarket.chat.R
import com.trustmarket.chat.presentation.components.SettingsItem
import com.trustmarket.chat.presentation.components.SettingsSection
import com.trustmarket.chat.presentation.components.ProfileEditDialog

/**
 * Settings screen for app configuration and user preferences
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalGlideComposeApi::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val currentUser by viewModel.currentUser.collectAsState()
    val notificationSettings by viewModel.notificationSettings.collectAsState()
    val privacySettings by viewModel.privacySettings.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()

    var showProfileEditDialog by remember { mutableStateOf(false) }
    var showSignOutDialog by remember { mutableStateOf(false) }
    var showDeleteAccountDialog by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        viewModel.loadUserSettings()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(id = R.string.settings)) },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Profile Section
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(20.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Profile Picture
                            Box(
                                modifier = Modifier
                                    .size(64.dp)
                                    .clip(CircleShape)
                            ) {
                                if (currentUser?.photoUrl != null) {
                                    GlideImage(
                                        model = currentUser?.photoUrl,
                                        contentDescription = "Profile Picture",
                                        modifier = Modifier
                                            .size(64.dp)
                                            .clip(CircleShape),
                                        contentScale = ContentScale.Crop
                                    )
                                } else {
                                    Card(
                                        modifier = Modifier.size(64.dp),
                                        shape = CircleShape,
                                        colors = CardDefaults.cardColors(
                                            containerColor = MaterialTheme.colorScheme.primary
                                        )
                                    ) {
                                        Box(
                                            modifier = Modifier.fillMaxSize(),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                text = currentUser?.displayName?.take(1)?.uppercase() ?: "؟",
                                                style = MaterialTheme.typography.headlineSmall,
                                                color = MaterialTheme.colorScheme.onPrimary,
                                                fontWeight = FontWeight.Bold
                                            )
                                        }
                                    }
                                }
                            }

                            Spacer(modifier = Modifier.width(16.dp))

                            // User Info
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = currentUser?.displayName ?: "مستخدم",
                                    style = MaterialTheme.typography.titleLarge,
                                    fontWeight = FontWeight.Bold
                                )
                                Text(
                                    text = currentUser?.email ?: "",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                )
                            }

                            // Edit Button
                            IconButton(
                                onClick = { showProfileEditDialog = true }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = stringResource(id = R.string.edit_profile)
                                )
                            }
                        }
                    }
                }
            }

            // Account Settings
            item {
                SettingsSection(title = "إعدادات الحساب") {
                    SettingsItem(
                        icon = Icons.Default.Person,
                        title = stringResource(id = R.string.edit_profile),
                        subtitle = "تعديل الاسم والصورة الشخصية",
                        onClick = { showProfileEditDialog = true }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Security,
                        title = "الخصوصية والأمان",
                        subtitle = "إعدادات الخصوصية وحظر المستخدمين",
                        onClick = { /* Navigate to privacy settings */ }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Backup,
                        title = "النسخ الاحتياطي",
                        subtitle = "نسخ احتياطي للرسائل والإعدادات",
                        onClick = { /* Navigate to backup settings */ }
                    )
                }
            }

            // Notification Settings
            item {
                SettingsSection(title = "الإشعارات") {
                    SettingsItem(
                        icon = Icons.Default.Notifications,
                        title = "إشعارات الرسائل",
                        subtitle = "تفعيل/إيقاف إشعارات الرسائل الجديدة",
                        trailing = {
                            Switch(
                                checked = notificationSettings.enableNotifications,
                                onCheckedChange = { 
                                    viewModel.updateNotificationSetting("enableNotifications", it)
                                }
                            )
                        }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Chat,
                        title = "إشعارات الدردشة الخاصة",
                        subtitle = "إشعارات للرسائل الخاصة",
                        trailing = {
                            Switch(
                                checked = notificationSettings.enablePrivateMessageNotifications,
                                onCheckedChange = { 
                                    viewModel.updateNotificationSetting("enablePrivateMessageNotifications", it)
                                }
                            )
                        }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.VolumeUp,
                        title = "الصوت والاهتزاز",
                        subtitle = "إعدادات الصوت والاهتزاز للإشعارات",
                        trailing = {
                            Switch(
                                checked = notificationSettings.vibrationEnabled,
                                onCheckedChange = { 
                                    viewModel.updateNotificationSetting("vibrationEnabled", it)
                                }
                            )
                        }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Schedule,
                        title = "الساعات الهادئة",
                        subtitle = "إيقاف الإشعارات في أوقات محددة",
                        trailing = {
                            Switch(
                                checked = notificationSettings.quietHoursEnabled,
                                onCheckedChange = { 
                                    viewModel.updateNotificationSetting("quietHoursEnabled", it)
                                }
                            )
                        }
                    )
                }
            }

            // App Settings
            item {
                SettingsSection(title = "إعدادات التطبيق") {
                    SettingsItem(
                        icon = Icons.Default.Language,
                        title = "اللغة",
                        subtitle = "العربية",
                        onClick = { /* Language selection */ }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Palette,
                        title = "المظهر",
                        subtitle = "فاتح، داكن، أو تلقائي",
                        onClick = { /* Theme selection */ }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Storage,
                        title = "التخزين",
                        subtitle = "إدارة مساحة التخزين والملفات",
                        onClick = { /* Storage management */ }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Download,
                        title = "التحديثات",
                        subtitle = "البحث عن تحديثات التطبيق",
                        onClick = { /* Check for updates */ }
                    )
                }
            }

            // Support & Info
            item {
                SettingsSection(title = "الدعم والمعلومات") {
                    SettingsItem(
                        icon = Icons.Default.Help,
                        title = "المساعدة والدعم",
                        subtitle = "الأسئلة الشائعة والتواصل مع الدعم",
                        onClick = { /* Help & Support */ }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Info,
                        title = "حول التطبيق",
                        subtitle = "معلومات التطبيق والإصدار",
                        onClick = { /* About app */ }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Policy,
                        title = "سياسة الخصوصية",
                        subtitle = "اطلع على سياسة الخصوصية",
                        onClick = { /* Privacy policy */ }
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.Gavel,
                        title = "شروط الاستخدام",
                        subtitle = "اطلع على شروط الاستخدام",
                        onClick = { /* Terms of service */ }
                    )
                }
            }

            // Account Actions
            item {
                SettingsSection(title = "إجراءات الحساب") {
                    SettingsItem(
                        icon = Icons.Default.Logout,
                        title = stringResource(id = R.string.sign_out),
                        subtitle = "تسجيل الخروج من الحساب",
                        onClick = { showSignOutDialog = true },
                        textColor = MaterialTheme.colorScheme.primary
                    )
                    
                    SettingsItem(
                        icon = Icons.Default.DeleteForever,
                        title = "حذف الحساب",
                        subtitle = "حذف الحساب نهائياً مع جميع البيانات",
                        onClick = { showDeleteAccountDialog = true },
                        textColor = MaterialTheme.colorScheme.error
                    )
                }
            }

            // Version Info
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = stringResource(id = R.string.app_name),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "الإصدار 1.0.0",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
            }
        }
    }

    // Profile Edit Dialog
    if (showProfileEditDialog) {
        ProfileEditDialog(
            currentUser = currentUser,
            onDismiss = { showProfileEditDialog = false },
            onSave = { name, bio ->
                viewModel.updateProfile(name, bio)
                showProfileEditDialog = false
            }
        )
    }

    // Sign Out Dialog
    if (showSignOutDialog) {
        AlertDialog(
            onDismissRequest = { showSignOutDialog = false },
            title = { Text("تسجيل الخروج") },
            text = { Text("هل أنت متأكد من تسجيل الخروج؟") },
            confirmButton = {
                Button(
                    onClick = {
                        viewModel.signOut()
                        showSignOutDialog = false
                        navController.navigate("auth") {
                            popUpTo(0) { inclusive = true }
                        }
                    }
                ) {
                    Text("تسجيل الخروج")
                }
            },
            dismissButton = {
                TextButton(onClick = { showSignOutDialog = false }) {
                    Text(stringResource(id = R.string.cancel))
                }
            }
        )
    }

    // Delete Account Dialog
    if (showDeleteAccountDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteAccountDialog = false },
            title = { Text("حذف الحساب") },
            text = { 
                Text("هل أنت متأكد من حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بياناتك نهائياً.")
            },
            confirmButton = {
                Button(
                    onClick = {
                        viewModel.deleteAccount()
                        showDeleteAccountDialog = false
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("حذف الحساب")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteAccountDialog = false }) {
                    Text(stringResource(id = R.string.cancel))
                }
            }
        )
    }

    // Error handling
    LaunchedEffect(errorMessage) {
        errorMessage?.let {
            // Show error snackbar
            viewModel.clearError()
        }
    }
}
