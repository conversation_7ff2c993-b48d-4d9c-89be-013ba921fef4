package com.trustmarket.chat.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * User data model representing a user in the chat application
 */
@Parcelize
data class User(
    val uid: String = "",
    val name: String = "",
    val email: String = "",
    val photoUrl: String? = null,
    val bio: String? = null,
    val phoneNumber: String? = null,
    val isOnline: Boolean = false,
    val lastSeen: Long = 0L,
    val isAdmin: Boolean = false,
    val isBanned: Boolean = false,
    val createdAt: Long = 0L,
    val updatedAt: Long = 0L,
    val fcmToken: String? = null,
    val language: String = "ar", // Default to Arabic
    val notificationSettings: NotificationSettings = NotificationSettings(),
    val privacySettings: PrivacySettings = PrivacySettings(),
    val luckyWheelData: LuckyWheelData = LuckyWheelData(),
    val rewardPoints: Int = 0,
    val usedRewardCodes: List<String> = emptyList()
) : Parcelable {
    
    /**
     * Check if user is currently online
     */
    fun isCurrentlyOnline(): Boolean {
        return isOnline && (System.currentTimeMillis() - lastSeen) < 300000 // 5 minutes
    }
    
    /**
     * Get display name (fallback to email if name is empty)
     */
    fun getDisplayName(): String {
        return if (name.isNotBlank()) name else email.substringBefore("@")
    }
    
    /**
     * Get formatted last seen time
     */
    fun getFormattedLastSeen(): String {
        if (isCurrentlyOnline()) return "متصل الآن"
        
        val timeDiff = System.currentTimeMillis() - lastSeen
        val minutes = timeDiff / (1000 * 60)
        val hours = minutes / 60
        val days = hours / 24
        
        return when {
            minutes < 1 -> "منذ لحظات"
            minutes < 60 -> "منذ ${minutes} دقيقة"
            hours < 24 -> "منذ ${hours} ساعة"
            days < 7 -> "منذ ${days} يوم"
            else -> "منذ أكثر من أسبوع"
        }
    }
}

/**
 * Notification settings for user
 */
@Parcelize
data class NotificationSettings(
    val enableNotifications: Boolean = true,
    val enablePrivateMessageNotifications: Boolean = true,
    val enableGroupMessageNotifications: Boolean = true,
    val enableLuckyWheelNotifications: Boolean = true,
    val enableAdminNotifications: Boolean = true,
    val notificationSound: String = "default",
    val vibrationEnabled: Boolean = true,
    val quietHoursEnabled: Boolean = false,
    val quietHoursStart: String = "22:00",
    val quietHoursEnd: String = "08:00"
) : Parcelable

/**
 * Privacy settings for user
 */
@Parcelize
data class PrivacySettings(
    val showOnlineStatus: Boolean = true,
    val showLastSeen: Boolean = true,
    val allowPrivateMessages: Boolean = true,
    val showProfilePhoto: Boolean = true,
    val showPhoneNumber: Boolean = false,
    val showEmail: Boolean = false,
    val blockedUsers: List<String> = emptyList()
) : Parcelable

/**
 * Lucky wheel data for user
 */
@Parcelize
data class LuckyWheelData(
    val lastSpinDate: String = "", // Format: yyyy-MM-dd
    val totalSpins: Int = 0,
    val totalWins: Int = 0,
    val totalRewards: Int = 0,
    val spinHistory: List<SpinResult> = emptyList()
) : Parcelable

/**
 * Spin result for lucky wheel
 */
@Parcelize
data class SpinResult(
    val date: Long = 0L,
    val reward: String = "",
    val points: Int = 0,
    val isWin: Boolean = false
) : Parcelable
