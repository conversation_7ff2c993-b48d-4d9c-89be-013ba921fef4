package com.trustmarket.chat.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Chat room data model
 */
@Parcelize
data class ChatRoom(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val imageUrl: String? = null,
    val ownerId: String = "",
    val ownerName: String = "",
    val createdAt: Long = 0L,
    val updatedAt: Long = 0L,
    val isPrivate: Boolean = false,
    val isDirectMessage: Boolean = false,
    val members: List<String> = emptyList(),
    val admins: List<String> = emptyList(),
    val bannedUsers: List<String> = emptyList(),
    val lastMessage: LastMessage? = null,
    val messageCount: Int = 0,
    val isActive: Boolean = true,
    val isDeleted: Boolean = false,
    val settings: RoomSettings = RoomSettings(),
    val specialMode: SpecialMode = SpecialMode()
) : Parcelable {
    
    /**
     * Check if user is owner of the room
     */
    fun isOwner(userId: String): Boolean {
        return ownerId == userId
    }
    
    /**
     * Check if user is admin of the room
     */
    fun isAdmin(userId: String): Boolean {
        return admins.contains(userId) || isOwner(userId)
    }
    
    /**
     * Check if user is member of the room
     */
    fun isMember(userId: String): Boolean {
        return members.contains(userId)
    }
    
    /**
     * Check if user is banned from the room
     */
    fun isBanned(userId: String): Boolean {
        return bannedUsers.contains(userId)
    }
    
    /**
     * Get member count
     */
    fun getMemberCount(): Int {
        return members.size
    }
    
    /**
     * Get display name for the room
     */
    fun getDisplayName(): String {
        return if (name.isNotBlank()) name else "غرفة دردشة"
    }
}

/**
 * Last message in the room
 */
@Parcelize
data class LastMessage(
    val id: String = "",
    val text: String = "",
    val senderId: String = "",
    val senderName: String = "",
    val timestamp: Long = 0L,
    val type: MessageType = MessageType.TEXT,
    val isRead: Boolean = false
) : Parcelable

/**
 * Room settings
 */
@Parcelize
data class RoomSettings(
    val allowFileSharing: Boolean = true,
    val allowImageSharing: Boolean = true,
    val allowAudioSharing: Boolean = true,
    val allowVideoSharing: Boolean = true,
    val maxFileSize: Long = 5 * 1024 * 1024, // 5MB
    val muteNotifications: Boolean = false,
    val autoDeleteMessages: Boolean = false,
    val autoDeleteDays: Int = 30,
    val requireApprovalToJoin: Boolean = false,
    val allowInviteLinks: Boolean = true,
    val maxMembers: Int = 1000
) : Parcelable

/**
 * Special mode settings for room
 */
@Parcelize
data class SpecialMode(
    val isEnabled: Boolean = false,
    val enabledBy: String = "",
    val enabledAt: Long = 0L,
    val description: String = "",
    val allowedUsers: List<String> = emptyList(),
    val messageTemplate: String = "",
    val autoResponses: List<AutoResponse> = emptyList()
) : Parcelable

/**
 * Auto response for special mode
 */
@Parcelize
data class AutoResponse(
    val trigger: String = "",
    val response: String = "",
    val isActive: Boolean = true
) : Parcelable

/**
 * Message types
 */
enum class MessageType {
    TEXT,
    IMAGE,
    VIDEO,
    AUDIO,
    DOCUMENT,
    LOCATION,
    CONTACT,
    STICKER,
    GIF,
    SPECIAL,
    SYSTEM,
    REPLY,
    FORWARD
}
