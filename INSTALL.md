# دليل التثبيت - Trust Market Python

## متطلبات النظام

### 1. تثبيت Python
يجب تثبيت Python 3.8 أو أحدث على النظام.

#### Windows:
1. اذهب إلى [python.org](https://www.python.org/downloads/)
2. حمل أحدث إصدار من Python 3
3. شغل ملف التثبيت
4. **مهم**: تأكد من تحديد "Add Python to PATH" أثناء التثبيت
5. أعد تشغيل Command Prompt أو PowerShell

#### macOS:
```bash
# باستخدام Homebrew
brew install python3

# أو حمل من الموقع الرسمي
# https://www.python.org/downloads/
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

### 2. التحقق من التثبيت
```bash
python --version
# أو
python3 --version
# أو
py --version
```

## خطوات التثبيت

### 1. إنشاء البيئة الافتراضية
```bash
# Windows
python -m venv venv
# أو
py -m venv venv

# macOS/Linux
python3 -m venv venv
```

### 2. تفعيل البيئة الافتراضية

#### Windows:
```bash
# PowerShell
venv\Scripts\Activate.ps1

# Command Prompt
venv\Scripts\activate.bat
```

#### macOS/Linux:
```bash
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد متغيرات البيئة
1. انسخ ملف `.env.example` إلى `.env`
2. املأ المتغيرات المطلوبة:

```env
# إعدادات Firebase
FIREBASE_PRIVATE_KEY_ID=your_private_key_id_here
FIREBASE_PRIVATE_KEY=your_private_key_here
FIREBASE_CLIENT_EMAIL=your_client_email_here
FIREBASE_CLIENT_ID=your_client_id_here
FIREBASE_CLIENT_CERT_URL=your_client_cert_url_here

# إعدادات التطبيق
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
```

### 5. تشغيل التطبيق
```bash
python app.py
```

التطبيق سيعمل على: `http://localhost:5000`

## إعداد Firebase

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع"
3. اتبع الخطوات لإنشاء المشروع

### 2. تفعيل Firestore
1. في لوحة تحكم Firebase، اذهب إلى "Firestore Database"
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية
4. اختر الموقع الجغرافي

### 3. تفعيل Authentication
1. اذهب إلى "Authentication"
2. انقر على "البدء"
3. في تبويب "Sign-in method"، فعل "Email/Password"

### 4. إنشاء Service Account
1. اذهب إلى "Project Settings" (أيقونة الترس)
2. انقر على تبويب "Service accounts"
3. انقر على "Generate new private key"
4. احفظ الملف JSON المحمل
5. انسخ المعلومات إلى ملف `.env`

## استكشاف الأخطاء

### خطأ: Python غير موجود
```bash
# تأكد من تثبيت Python وإضافته إلى PATH
python --version
```

### خطأ: pip غير موجود
```bash
# Windows
python -m ensurepip --upgrade

# macOS/Linux
sudo apt install python3-pip
```

### خطأ: لا يمكن تفعيل البيئة الافتراضية
```bash
# Windows PowerShell - قد تحتاج لتغيير سياسة التنفيذ
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### خطأ: Firebase connection
1. تأكد من صحة معلومات Firebase في `.env`
2. تأكد من تفعيل Firestore و Authentication
3. تحقق من صحة Service Account Key

### خطأ: Port already in use
```bash
# غير المنفذ في app.py أو أوقف العملية التي تستخدم المنفذ 5000
netstat -ano | findstr :5000
taskkill /PID <PID_NUMBER> /F
```

## الأوامر المفيدة

### تحديث المتطلبات
```bash
pip freeze > requirements.txt
```

### تشغيل في وضع التطوير
```bash
export FLASK_ENV=development  # Linux/macOS
set FLASK_ENV=development     # Windows
python app.py
```

### تشغيل مع إعادة التحميل التلقائي
```bash
flask run --reload
```

### إيقاف تشغيل البيئة الافتراضية
```bash
deactivate
```

## الدعم
إذا واجهت أي مشاكل:
1. تأكد من تثبيت Python بشكل صحيح
2. تأكد من تفعيل البيئة الافتراضية
3. تحقق من ملف `.env`
4. راجع سجلات الأخطاء في Terminal

للمساعدة الإضافية، تواصل معنا على:
- البريد الإلكتروني: <EMAIL>
- GitHub Issues: [رابط المشروع]
