@echo off
echo ========================================
echo    Trust Market - Python Chat App
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [خطأ] Python غير مثبت على النظام
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    echo تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    pause
    exit /b 1
)

echo [✓] Python مثبت بنجاح

REM التحقق من وجود البيئة الافتراضية
if not exist "venv" (
    echo [!] إنشاء البيئة الافتراضية...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo [خطأ] فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
    echo [✓] تم إنشاء البيئة الافتراضية
)

REM تفعيل البيئة الافتراضية
echo [!] تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo [خطأ] فشل في تفعيل البيئة الافتراضية
    pause
    exit /b 1
)

echo [✓] تم تفعيل البيئة الافتراضية

REM تثبيت المتطلبات
echo [!] تثبيت المتطلبات...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [خطأ] فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo [✓] تم تثبيت المتطلبات بنجاح

REM تشغيل التطبيق
echo.
echo ========================================
echo تشغيل التطبيق...
echo الرابط: http://localhost:5000
echo للإيقاف: اضغط Ctrl+C
echo ========================================
echo.

python run.py

pause
