{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - Trust Market{% endblock %}

{% block content %}
<div class="home-container">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            <i class="fas fa-shield-alt text-primary"></i>
                            Trust Market
                        </h1>
                        <h2 class="hero-subtitle">منصة آمنة للبيع والشراء</h2>
                        <p class="hero-description">
                            انضم إلى مجتمعنا الآمن للتجارة والتواصل. 
                            دردش مع البائعين والمشترين في بيئة محمية وموثوقة.
                        </p>
                        
                        <div class="hero-actions">
                            {% if session.user_id %}
                                <a href="{{ url_for('chat') }}" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-comments"></i>
                                    ابدأ الدردشة
                                </a>
                                <a href="{{ url_for('private_chats') }}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-user-friends"></i>
                                    الدردشات الخاصة
                                </a>
                            {% else %}
                                <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-sign-in-alt"></i>
                                    تسجيل الدخول
                                </a>
                                <a href="{{ url_for('login') }}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-user-plus"></i>
                                    إنشاء حساب
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="hero-image">
                        <div class="chat-preview">
                            <div class="chat-window">
                                <div class="chat-header-preview">
                                    <div class="chat-user">
                                        <div class="user-avatar bg-primary">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="user-info">
                                            <div class="user-name">أحمد محمد</div>
                                            <div class="user-status">متصل الآن</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="chat-messages-preview">
                                    <div class="message-preview other">
                                        <div class="message-content">
                                            مرحباً، هل المنتج متوفر؟
                                        </div>
                                    </div>
                                    <div class="message-preview own">
                                        <div class="message-content">
                                            نعم متوفر، كم الكمية المطلوبة؟
                                        </div>
                                    </div>
                                    <div class="message-preview other">
                                        <div class="message-content">
                                            أريد قطعتين من فضلك
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="chat-input-preview">
                                    <div class="input-preview">
                                        <span class="typing-text">اكتب رسالتك...</span>
                                        <div class="send-btn-preview">
                                            <i class="fas fa-paper-plane"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h3 class="section-title">لماذا Trust Market؟</h3>
                    <p class="section-subtitle">ميزات تجعل تجربتك آمنة ومريحة</p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h5 class="feature-title">أمان عالي</h5>
                        <p class="feature-description">
                            نظام حماية متقدم يضمن أمان بياناتك ومحادثاتك
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h5 class="feature-title">سرعة فائقة</h5>
                        <p class="feature-description">
                            رسائل فورية وتحديثات في الوقت الفعلي
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="feature-title">مجتمع موثوق</h5>
                        <p class="feature-description">
                            مجتمع من المستخدمين الموثوقين والمتحققين
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h5 class="feature-title">متوافق مع الجوال</h5>
                        <p class="feature-description">
                            تصميم متجاوب يعمل بسلاسة على جميع الأجهزة
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h5 class="feature-title">متاح 24/7</h5>
                        <p class="feature-description">
                            خدمة متاحة على مدار الساعة طوال أيام الأسبوع
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <h5 class="feature-title">دعم العربية</h5>
                        <p class="feature-description">
                            واجهة مصممة خصيصاً للمستخدمين العرب
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section py-5">
        <div class="container">
            <div class="row g-4 text-center">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">1000+</div>
                        <div class="stat-label">مستخدم نشط</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">50000+</div>
                        <div class="stat-label">رسالة يومياً</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">99.9%</div>
                        <div class="stat-label">وقت التشغيل</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">دعم فني</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3 class="cta-title">جاهز للبدء؟</h3>
                    <p class="cta-description">
                        انضم إلى آلاف المستخدمين الذين يثقون في منصتنا
                    </p>
                    
                    {% if not session.user_id %}
                    <div class="cta-actions">
                        <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-rocket"></i>
                            ابدأ الآن مجاناً
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-secondary) 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
        color: var(--text-light);
    }

    .hero-subtitle {
        font-size: 1.8rem;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
    }

    .hero-description {
        font-size: 1.2rem;
        color: var(--text-muted);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .chat-preview {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .chat-window {
        width: 300px;
        background-color: var(--bg-secondary);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .chat-header-preview {
        padding: 1rem;
        background-color: var(--primary-color);
        color: white;
    }

    .chat-user {
        display: flex;
        align-items: center;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
    }

    .user-name {
        font-weight: bold;
        font-size: 0.9rem;
    }

    .user-status {
        font-size: 0.8rem;
        opacity: 0.8;
    }

    .chat-messages-preview {
        padding: 1rem;
        height: 200px;
        overflow-y: auto;
    }

    .message-preview {
        margin-bottom: 1rem;
        display: flex;
    }

    .message-preview.own {
        justify-content: flex-end;
    }

    .message-preview .message-content {
        max-width: 80%;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-size: 0.9rem;
    }

    .message-preview.own .message-content {
        background-color: var(--primary-color);
        color: white;
        border-bottom-left-radius: 5px;
    }

    .message-preview.other .message-content {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text-light);
        border-bottom-right-radius: 5px;
    }

    .chat-input-preview {
        padding: 1rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .input-preview {
        display: flex;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 25px;
        padding: 0.5rem 1rem;
    }

    .typing-text {
        flex: 1;
        color: var(--text-muted);
        font-size: 0.9rem;
    }

    .send-btn-preview {
        color: var(--primary-color);
        margin-right: 0.5rem;
    }

    .features-section {
        background-color: var(--bg-secondary);
    }

    .section-title {
        font-size: 2.5rem;
        color: var(--text-light);
        margin-bottom: 1rem;
    }

    .section-subtitle {
        font-size: 1.2rem;
        color: var(--text-muted);
    }

    .feature-card {
        background-color: var(--bg-dark);
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        height: 100%;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: transform 0.3s;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .feature-icon {
        font-size: 3rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .feature-title {
        color: var(--text-light);
        margin-bottom: 1rem;
    }

    .feature-description {
        color: var(--text-muted);
        line-height: 1.6;
    }

    .stats-section {
        background-color: var(--bg-dark);
    }

    .stat-card {
        padding: 2rem;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 1.1rem;
        color: var(--text-light);
    }

    .cta-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
        color: white;
    }

    .cta-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .cta-description {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.9;
    }

    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }
        
        .hero-subtitle {
            font-size: 1.5rem;
        }
        
        .chat-window {
            width: 250px;
        }
        
        .section-title {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}
