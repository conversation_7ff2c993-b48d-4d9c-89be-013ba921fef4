<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Chat Bubble Styles -->
    <style name="ChatBubble">
        <item name="android:layout_margin">@dimen/chat_bubble_margin</item>
        <item name="android:padding">@dimen/chat_bubble_padding</item>
        <item name="android:layout_marginStart">@dimen/spacing_large</item>
        <item name="android:layout_marginEnd">@dimen/spacing_medium</item>
    </style>

    <style name="ChatBubble.Sent">
        <item name="android:background">@drawable/chat_bubble_sent</item>
        <item name="android:layout_marginStart">@dimen/spacing_extra_large</item>
        <item name="android:layout_marginEnd">@dimen/spacing_medium</item>
    </style>

    <style name="ChatBubble.Received">
        <item name="android:background">@drawable/chat_bubble_received</item>
        <item name="android:layout_marginStart">@dimen/spacing_medium</item>
        <item name="android:layout_marginEnd">@dimen/spacing_extra_large</item>
    </style>

    <!-- Button Styles -->
    <style name="Button.Primary" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="cornerRadius">@dimen/button_corner_radius</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:minHeight">@dimen/button_height</item>
    </style>

    <style name="Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/primary</item>
        <item name="strokeColor">@color/primary</item>
        <item name="cornerRadius">@dimen/button_corner_radius</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:minHeight">@dimen/button_height</item>
    </style>

    <style name="Button.Text" parent="Widget.Material3.Button.TextButton">
        <item name="android:textColor">@color/primary</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:minHeight">@dimen/button_height</item>
    </style>

    <!-- Card Styles -->
    <style name="Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">@dimen/card_corner_radius</item>
        <item name="cardElevation">@dimen/card_elevation</item>
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="android:layout_margin">@dimen/spacing_small</item>
    </style>

    <style name="Card.Flat" parent="Widget.Material3.CardView.Filled">
        <item name="cardCornerRadius">@dimen/card_corner_radius</item>
        <item name="cardElevation">0dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="android:layout_margin">@dimen/spacing_small</item>
    </style>

    <!-- Text Styles -->
    <style name="Text.Headline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="Text.Title" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="Text.Body" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="Text.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <!-- Input Field Styles -->
    <style name="TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/text_hint</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="boxCornerRadiusTopStart">@dimen/button_corner_radius</item>
        <item name="boxCornerRadiusTopEnd">@dimen/button_corner_radius</item>
        <item name="boxCornerRadiusBottomStart">@dimen/button_corner_radius</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/button_corner_radius</item>
    </style>

    <!-- Avatar Styles -->
    <style name="Avatar">
        <item name="android:layout_width">@dimen/avatar_size_medium</item>
        <item name="android:layout_height">@dimen/avatar_size_medium</item>
        <item name="android:scaleType">centerCrop</item>
    </style>

    <style name="Avatar.Small">
        <item name="android:layout_width">@dimen/avatar_size_small</item>
        <item name="android:layout_height">@dimen/avatar_size_small</item>
    </style>

    <style name="Avatar.Large">
        <item name="android:layout_width">@dimen/avatar_size_large</item>
        <item name="android:layout_height">@dimen/avatar_size_large</item>
    </style>

    <!-- Icon Styles -->
    <style name="Icon">
        <item name="android:layout_width">@dimen/icon_size_medium</item>
        <item name="android:layout_height">@dimen/icon_size_medium</item>
        <item name="android:tint">@color/text_primary</item>
    </style>

    <style name="Icon.Small">
        <item name="android:layout_width">@dimen/icon_size_small</item>
        <item name="android:layout_height">@dimen/icon_size_small</item>
    </style>

    <style name="Icon.Large">
        <item name="android:layout_width">@dimen/icon_size_large</item>
        <item name="android:layout_height">@dimen/icon_size_large</item>
    </style>

    <!-- Divider Style -->
    <style name="Divider">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/divider_height</item>
        <item name="android:background">@color/divider</item>
    </style>

    <!-- Progress Indicator Styles -->
    <style name="ProgressIndicator" parent="Widget.Material3.CircularProgressIndicator">
        <item name="android:layout_width">@dimen/progress_size_medium</item>
        <item name="android:layout_height">@dimen/progress_size_medium</item>
        <item name="indicatorColor">@color/primary</item>
    </style>

    <style name="ProgressIndicator.Small">
        <item name="android:layout_width">@dimen/progress_size_small</item>
        <item name="android:layout_height">@dimen/progress_size_small</item>
    </style>

    <style name="ProgressIndicator.Large">
        <item name="android:layout_width">@dimen/progress_size_large</item>
        <item name="android:layout_height">@dimen/progress_size_large</item>
    </style>

    <!-- Settings Item Style -->
    <style name="SettingsItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/settings_item_height</item>
        <item name="android:paddingStart">@dimen/spacing_medium</item>
        <item name="android:paddingEnd">@dimen/spacing_medium</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
        <item name="android:minHeight">@dimen/min_touch_target</item>
    </style>

    <!-- Floating Action Button Style -->
    <style name="FloatingActionButton" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="backgroundTint">@color/primary</item>
        <item name="tint">@color/white</item>
        <item name="android:layout_margin">@dimen/spacing_medium</item>
    </style>

    <!-- Bottom Navigation Style -->
    <style name="BottomNavigation" parent="Widget.Material3.BottomNavigationView">
        <item name="android:layout_height">@dimen/bottom_nav_height</item>
        <item name="android:background">@color/surface</item>
        <item name="itemIconTint">@color/primary</item>
        <item name="itemTextColor">@color/primary</item>
        <item name="elevation">@dimen/toolbar_elevation</item>
    </style>

    <!-- Toolbar Style -->
    <style name="Toolbar" parent="Widget.Material3.Toolbar">
        <item name="android:layout_height">@dimen/toolbar_height</item>
        <item name="android:background">@color/primary</item>
        <item name="titleTextColor">@color/white</item>
        <item name="android:elevation">@dimen/toolbar_elevation</item>
    </style>
</resources>
