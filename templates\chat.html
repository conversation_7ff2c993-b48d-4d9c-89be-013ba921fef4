{% extends "base.html" %}

{% block title %}الدردشة - Trust Market{% endblock %}

{% block content %}
<div class="chat-container">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- قائمة الغرف -->
            <div class="col-lg-3 col-md-4 d-none d-md-block">
                <div class="rooms-sidebar">
                    <div class="sidebar-header">
                        <h5><i class="fas fa-comments"></i> الغرف</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshRooms()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="rooms-list" id="roomsList">
                        <!-- قائمة الغرف ستظهر هنا -->
                    </div>
                </div>
            </div>

            <!-- منطقة الدردشة -->
            <div class="col-lg-9 col-md-8 col-12">
                <div class="chat-area">
                    <!-- رأس الدردشة -->
                    <div class="chat-header">
                        <div class="d-flex align-items-center">
                            <button class="btn btn-outline-light d-md-none me-2" onclick="toggleSidebar()">
                                <i class="fas fa-bars"></i>
                            </button>
                            <div class="room-info">
                                <h6 class="mb-0" id="currentRoomName">اختر غرفة للدردشة</h6>
                                <small class="text-muted" id="currentRoomDescription"></small>
                            </div>
                        </div>
                        <div class="chat-actions">
                            <span class="badge bg-secondary" id="userCount">0 مستخدم</span>
                        </div>
                    </div>

                    <!-- منطقة الرسائل -->
                    <div class="messages-area" id="messagesArea">
                        <div class="welcome-message text-center">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">مرحباً بك في Trust Market</h5>
                            <p class="text-muted">اختر غرفة من القائمة الجانبية لبدء الدردشة</p>
                        </div>
                    </div>

                    <!-- مؤشر الكتابة -->
                    <div class="typing-indicator" id="typingIndicator" style="display: none;">
                        <small class="text-muted">
                            <span id="typingUsers"></span> يكتب...
                            <span class="typing-dots">
                                <span>.</span><span>.</span><span>.</span>
                            </span>
                        </small>
                    </div>

                    <!-- منطقة إدخال الرسائل -->
                    <div class="message-input-area" id="messageInputArea" style="display: none;">
                        <form id="messageForm" class="d-flex">
                            <input type="text" class="form-control me-2" id="messageInput" 
                                   placeholder="اكتب رسالتك هنا..." disabled>
                            <button type="submit" class="btn btn-primary" id="sendBtn" disabled>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الغرف للجوال -->
<div class="offcanvas offcanvas-start" tabindex="-1" id="roomsOffcanvas">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title">الغرف</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body">
        <div class="rooms-list" id="mobileRoomsList">
            <!-- قائمة الغرف للجوال -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // متغيرات عامة
    let socket = null;
    let currentRoom = null;
    let currentUser = null;
    let typingTimer = null;
    let isTyping = false;

    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        initializeChat();
        loadRooms();
    });

    // تهيئة الدردشة
    function initializeChat() {
        // إعداد Socket.IO
        socket = io();
        
        // معلومات المستخدم الحالي (من الجلسة)
        currentUser = {
            uid: '{{ session.user_id }}',
            name: '{{ session.name }}',
            email: '{{ session.email }}'
        };

        // أحداث Socket.IO
        socket.on('connect', function() {
            console.log('متصل بالخادم');
        });

        socket.on('disconnect', function() {
            console.log('منقطع عن الخادم');
        });

        socket.on('user_joined', function(data) {
            addSystemMessage(`${data.user.name} انضم إلى الدردشة`);
        });

        socket.on('user_left', function(data) {
            addSystemMessage(`${data.user.name} غادر الدردشة`);
        });

        socket.on('new_message', function(message) {
            addMessage(message);
        });

        socket.on('user_typing', function(data) {
            handleTypingIndicator(data);
        });

        socket.on('error', function(data) {
            showToast(data.message, 'error');
        });

        // إعداد نموذج الرسائل
        document.getElementById('messageForm').addEventListener('submit', function(e) {
            e.preventDefault();
            sendMessage();
        });

        // إعداد مؤشر الكتابة
        document.getElementById('messageInput').addEventListener('input', function() {
            handleTyping();
        });
    }

    // تحميل قائمة الغرف
    function loadRooms() {
        fetch('/api/rooms')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayRooms(data.rooms);
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الغرف:', error);
            });
    }

    // عرض قائمة الغرف
    function displayRooms(rooms) {
        const roomsList = document.getElementById('roomsList');
        const mobileRoomsList = document.getElementById('mobileRoomsList');
        
        const roomsHTML = rooms.map(room => `
            <div class="room-item" onclick="joinRoom('${room.id}', '${room.name}', '${room.description}')">
                <div class="room-info">
                    <h6 class="room-name">${room.name}</h6>
                    <small class="room-description">${room.description}</small>
                </div>
                <div class="room-stats">
                    <span class="badge bg-primary">${room.memberCount}</span>
                </div>
            </div>
        `).join('');
        
        roomsList.innerHTML = roomsHTML;
        mobileRoomsList.innerHTML = roomsHTML;
    }

    // الانضمام إلى غرفة
    function joinRoom(roomId, roomName, roomDescription) {
        if (currentRoom === roomId) return;
        
        // مغادرة الغرفة الحالية
        if (currentRoom) {
            socket.emit('leave_room', {
                room_id: currentRoom,
                user: currentUser
            });
        }
        
        // الانضمام إلى الغرفة الجديدة
        currentRoom = roomId;
        socket.emit('join_room', {
            room_id: roomId,
            user: currentUser
        });
        
        // تحديث واجهة المستخدم
        document.getElementById('currentRoomName').textContent = roomName;
        document.getElementById('currentRoomDescription').textContent = roomDescription;
        document.getElementById('messagesArea').innerHTML = '';
        document.getElementById('messageInputArea').style.display = 'flex';
        document.getElementById('messageInput').disabled = false;
        document.getElementById('sendBtn').disabled = false;
        
        // إغلاق القائمة الجانبية في الجوال
        const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('roomsOffcanvas'));
        if (offcanvas) {
            offcanvas.hide();
        }
        
        showToast(`انضممت إلى ${roomName}`, 'success');
    }

    // إرسال رسالة
    function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const content = messageInput.value.trim();
        
        if (!content || !currentRoom) return;
        
        socket.emit('send_message', {
            room_id: currentRoom,
            content: content,
            user: currentUser
        });
        
        messageInput.value = '';
        stopTyping();
    }

    // إضافة رسالة إلى الدردشة
    function addMessage(message) {
        const messagesArea = document.getElementById('messagesArea');
        const isOwnMessage = message.author.uid === currentUser.uid;
        
        const messageElement = document.createElement('div');
        messageElement.className = `message ${isOwnMessage ? 'own-message' : 'other-message'}`;
        
        const messageTime = new Date(message.createdAt).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        messageElement.innerHTML = `
            <div class="message-content">
                ${!isOwnMessage ? `<div class="message-author">${message.author.name}</div>` : ''}
                <div class="message-text">${message.content}</div>
                <div class="message-time">${messageTime}</div>
            </div>
        `;
        
        messagesArea.appendChild(messageElement);
        messagesArea.scrollTop = messagesArea.scrollHeight;
    }

    // إضافة رسالة نظام
    function addSystemMessage(content) {
        const messagesArea = document.getElementById('messagesArea');
        
        const messageElement = document.createElement('div');
        messageElement.className = 'message system-message';
        messageElement.innerHTML = `
            <div class="message-content">
                <div class="message-text">${content}</div>
            </div>
        `;
        
        messagesArea.appendChild(messageElement);
        messagesArea.scrollTop = messagesArea.scrollHeight;
    }

    // معالجة مؤشر الكتابة
    function handleTyping() {
        if (!currentRoom) return;
        
        if (!isTyping) {
            isTyping = true;
            socket.emit('typing_start', {
                room_id: currentRoom,
                user: currentUser
            });
        }
        
        clearTimeout(typingTimer);
        typingTimer = setTimeout(stopTyping, 1000);
    }

    // إيقاف مؤشر الكتابة
    function stopTyping() {
        if (isTyping && currentRoom) {
            isTyping = false;
            socket.emit('typing_stop', {
                room_id: currentRoom,
                user: currentUser
            });
        }
        clearTimeout(typingTimer);
    }

    // معالجة مؤشر الكتابة من المستخدمين الآخرين
    function handleTypingIndicator(data) {
        const typingIndicator = document.getElementById('typingIndicator');
        const typingUsers = document.getElementById('typingUsers');
        
        if (data.typing) {
            typingUsers.textContent = data.user.name;
            typingIndicator.style.display = 'block';
        } else {
            typingIndicator.style.display = 'none';
        }
    }

    // تحديث قائمة الغرف
    function refreshRooms() {
        loadRooms();
        showToast('تم تحديث قائمة الغرف', 'success');
    }

    // تبديل القائمة الجانبية للجوال
    function toggleSidebar() {
        const offcanvas = new bootstrap.Offcanvas(document.getElementById('roomsOffcanvas'));
        offcanvas.show();
    }
</script>
{% endblock %}
