package com.trustmarket.chat.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.trustmarket.chat.presentation.home.HomeScreen
import com.trustmarket.chat.presentation.chat.ChatScreen
import com.trustmarket.chat.presentation.privatechat.PrivateChatScreen
import com.trustmarket.chat.presentation.luckywheel.LuckyWheelScreen
import com.trustmarket.chat.presentation.rewardcode.RewardCodeScreen
import com.trustmarket.chat.presentation.settings.SettingsScreen

/**
 * Navigation setup for the app
 */
@Composable
fun Navigation(navController: NavHostController) {
    NavHost(
        navController = navController,
        startDestination = "home"
    ) {
        // Home Screen
        composable("home") {
            HomeScreen(navController = navController)
        }
        
        // Chat Screen
        composable("chat/{roomId}") { backStackEntry ->
            val roomId = backStackEntry.arguments?.getString("roomId") ?: ""
            ChatScreen(
                roomId = roomId,
                navController = navController
            )
        }
        
        // Private Chat Screen
        composable("private_chat/{userId}") { backStackEntry ->
            val userId = backStackEntry.arguments?.getString("userId") ?: ""
            PrivateChatScreen(
                otherUserId = userId,
                navController = navController
            )
        }
        
        // Private Chats List
        composable("private_chats") {
            // TODO: Create PrivateChatsListScreen
            HomeScreen(navController = navController) // Placeholder
        }
        
        // Lucky Wheel Screen
        composable("lucky_wheel") {
            LuckyWheelScreen(navController = navController)
        }
        
        // Reward Code Screen
        composable("reward_code") {
            RewardCodeScreen(navController = navController)
        }
        
        // Notifications Screen
        composable("notifications") {
            // TODO: Create NotificationsScreen
            HomeScreen(navController = navController) // Placeholder
        }
        
        // Settings Screen
        composable("settings") {
            SettingsScreen(navController = navController)
        }
        
        // Admin Screen
        composable("admin") {
            // TODO: Create AdminScreen
            HomeScreen(navController = navController) // Placeholder
        }
    }
}
