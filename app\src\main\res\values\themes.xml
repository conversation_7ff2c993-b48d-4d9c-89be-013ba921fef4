<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme -->
    <style name="Theme.TrustMarketChat" parent="Theme.Material3.DayNight">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/white</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/surface</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Window background -->
        <item name="android:windowBackground">@color/background</item>
        
        <!-- Action bar -->
        <item name="actionBarTheme">@style/ThemeOverlay.Material3.ActionBar</item>
        
        <!-- Text appearance -->
        <item name="textAppearanceHeadline1">@style/TextAppearance.TrustMarketChat.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.TrustMarketChat.Headline2</item>
        <item name="textAppearanceBody1">@style/TextAppearance.TrustMarketChat.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.TrustMarketChat.Body2</item>
        
        <!-- Button styles -->
        <item name="materialButtonStyle">@style/Widget.TrustMarketChat.Button</item>
        <item name="borderlessButtonStyle">@style/Widget.TrustMarketChat.Button.Borderless</item>
        
        <!-- Card style -->
        <item name="materialCardViewStyle">@style/Widget.TrustMarketChat.CardView</item>
        
        <!-- Text field style -->
        <item name="textInputStyle">@style/Widget.TrustMarketChat.TextInputLayout</item>
    </style>

    <!-- Text Appearances -->
    <style name="TextAppearance.TrustMarketChat.Headline1" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.TrustMarketChat.Headline2" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.TrustMarketChat.Body1" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.TrustMarketChat.Body2" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <!-- Button Styles -->
    <style name="Widget.TrustMarketChat.Button" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="Widget.TrustMarketChat.Button.Borderless" parent="Widget.Material3.Button.TextButton">
        <item name="android:textColor">@color/primary</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <!-- Card Style -->
    <style name="Widget.TrustMarketChat.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
    </style>

    <!-- Text Input Layout Style -->
    <style name="Widget.TrustMarketChat.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/text_hint</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
    </style>

    <!-- Chat Bubble Styles -->
    <style name="ChatBubble.Sent">
        <item name="android:background">@drawable/chat_bubble_sent</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_marginStart">48dp</item>
        <item name="android:layout_marginEnd">16dp</item>
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style>

    <style name="ChatBubble.Received">
        <item name="android:background">@drawable/chat_bubble_received</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_marginStart">16dp</item>
        <item name="android:layout_marginEnd">48dp</item>
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="Theme.TrustMarketChat.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/primary</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_launcher_foreground</item>
        <item name="windowSplashScreenAnimationDuration">1000</item>
        <item name="postSplashScreenTheme">@style/Theme.TrustMarketChat</item>
    </style>

    <!-- No Action Bar Theme -->
    <style name="Theme.TrustMarketChat.NoActionBar" parent="Theme.TrustMarketChat">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Full Screen Theme -->
    <style name="Theme.TrustMarketChat.FullScreen" parent="Theme.TrustMarketChat.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
