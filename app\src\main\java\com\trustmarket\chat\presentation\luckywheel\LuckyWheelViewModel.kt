package com.trustmarket.chat.presentation.luckywheel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.trustmarket.chat.data.model.LuckyWheelPrize
import com.trustmarket.chat.data.model.SpinResult
import com.trustmarket.chat.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import kotlin.random.Random

/**
 * ViewModel for Lucky Wheel screen
 */
@HiltViewModel
class LuckyWheelViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _prizes = MutableStateFlow<List<LuckyWheelPrize>>(emptyList())
    val prizes: StateFlow<List<LuckyWheelPrize>> = _prizes.asStateFlow()

    private val _canSpin = MutableStateFlow(false)
    val canSpin: StateFlow<Boolean> = _canSpin.asStateFlow()

    private val _isSpinning = MutableStateFlow(false)
    val isSpinning: StateFlow<Boolean> = _isSpinning.asStateFlow()

    private val _spinResult = MutableStateFlow<SpinResult?>(null)
    val spinResult: StateFlow<SpinResult?> = _spinResult.asStateFlow()

    private val _userPoints = MutableStateFlow(0)
    val userPoints: StateFlow<Int> = _userPoints.asStateFlow()

    private val _spinHistory = MutableStateFlow<List<SpinResult>>(emptyList())
    val spinHistory: StateFlow<List<SpinResult>> = _spinHistory.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    init {
        loadUserData()
    }

    /**
     * Load user data including points and spin history
     */
    private fun loadUserData() {
        viewModelScope.launch {
            try {
                val currentUser = authRepository.getCurrentFirebaseUser()
                if (currentUser != null) {
                    // TODO: Load user points from Firebase
                    _userPoints.value = 150 // Placeholder
                    
                    // TODO: Load spin history from Firebase
                    loadSpinHistory()
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحميل بيانات المستخدم"
            }
        }
    }

    /**
     * Load available prizes
     */
    fun loadPrizes() {
        viewModelScope.launch {
            try {
                // TODO: Load prizes from Firebase
                // For now, create default prizes
                val defaultPrizes = listOf(
                    LuckyWheelPrize(
                        id = "1",
                        name = "جائزة كبرى",
                        description = "جائزة رائعة!",
                        points = 100,
                        probability = 0.05f,
                        color = "#FF6B6B",
                        textColor = "#FFFFFF"
                    ),
                    LuckyWheelPrize(
                        id = "2",
                        name = "جائزة متوسطة",
                        description = "جائزة جيدة",
                        points = 50,
                        probability = 0.15f,
                        color = "#4ECDC4",
                        textColor = "#FFFFFF"
                    ),
                    LuckyWheelPrize(
                        id = "3",
                        name = "جائزة صغيرة",
                        description = "جائزة لطيفة",
                        points = 25,
                        probability = 0.25f,
                        color = "#45B7D1",
                        textColor = "#FFFFFF"
                    ),
                    LuckyWheelPrize(
                        id = "4",
                        name = "نقاط إضافية",
                        description = "نقاط مفيدة",
                        points = 10,
                        probability = 0.35f,
                        color = "#96CEB4",
                        textColor = "#FFFFFF"
                    ),
                    LuckyWheelPrize(
                        id = "5",
                        name = "حظ أوفر",
                        description = "لا شيء هذه المرة",
                        points = 0,
                        probability = 0.20f,
                        color = "#FFEAA7",
                        textColor = "#333333"
                    )
                )
                
                _prizes.value = defaultPrizes
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في تحميل الجوائز"
            }
        }
    }

    /**
     * Check if user can spin today
     */
    fun checkCanSpin() {
        viewModelScope.launch {
            try {
                val currentUser = authRepository.getCurrentFirebaseUser()
                if (currentUser != null) {
                    // TODO: Check last spin date from Firebase
                    // For now, allow spinning once per day
                    val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
                    val lastSpinDate = getLastSpinDate() // TODO: Get from Firebase
                    
                    _canSpin.value = lastSpinDate != today
                }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في فحص إمكانية الدوران"
            }
        }
    }

    /**
     * Spin the wheel
     */
    fun spinWheel() {
        viewModelScope.launch {
            if (!_canSpin.value || _isSpinning.value) return@launch

            _isSpinning.value = true
            
            try {
                // Simulate spinning delay
                delay(3000)
                
                // Calculate result based on probabilities
                val result = calculateSpinResult()
                _spinResult.value = result
                
                // Update user points if won
                if (result.isWin) {
                    _userPoints.value += result.points
                    // TODO: Update points in Firebase
                }
                
                // Save spin result
                saveSpinResult(result)
                
                // Update can spin status
                _canSpin.value = false
                
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "فشل في دوران العجلة"
                _isSpinning.value = false
            }
        }
    }

    /**
     * Finish spinning animation
     */
    fun finishSpin() {
        _isSpinning.value = false
    }

    /**
     * Calculate spin result based on prize probabilities
     */
    private fun calculateSpinResult(): SpinResult {
        val random = Random.nextFloat()
        var cumulativeProbability = 0f
        
        for (prize in _prizes.value) {
            cumulativeProbability += prize.probability
            if (random <= cumulativeProbability) {
                return SpinResult(
                    date = System.currentTimeMillis(),
                    reward = prize.name,
                    points = prize.points,
                    isWin = prize.points > 0
                )
            }
        }
        
        // Fallback to last prize
        val lastPrize = _prizes.value.lastOrNull()
        return SpinResult(
            date = System.currentTimeMillis(),
            reward = lastPrize?.name ?: "حظ أوفر",
            points = lastPrize?.points ?: 0,
            isWin = (lastPrize?.points ?: 0) > 0
        )
    }

    /**
     * Save spin result to history
     */
    private suspend fun saveSpinResult(result: SpinResult) {
        try {
            // TODO: Save to Firebase
            val currentHistory = _spinHistory.value.toMutableList()
            currentHistory.add(0, result) // Add to beginning
            
            // Keep only last 50 results
            if (currentHistory.size > 50) {
                currentHistory.removeAt(currentHistory.size - 1)
            }
            
            _spinHistory.value = currentHistory
            
            // Update last spin date
            val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
            saveLastSpinDate(today)
            
        } catch (e: Exception) {
            // Handle error silently for history
        }
    }

    /**
     * Load spin history
     */
    private suspend fun loadSpinHistory() {
        try {
            // TODO: Load from Firebase
            // For now, create sample history
            val sampleHistory = listOf(
                SpinResult(
                    date = System.currentTimeMillis() - 86400000, // Yesterday
                    reward = "جائزة متوسطة",
                    points = 50,
                    isWin = true
                ),
                SpinResult(
                    date = System.currentTimeMillis() - 172800000, // 2 days ago
                    reward = "حظ أوفر",
                    points = 0,
                    isWin = false
                )
            )
            _spinHistory.value = sampleHistory
        } catch (e: Exception) {
            // Handle error silently
        }
    }

    /**
     * Get last spin date
     */
    private fun getLastSpinDate(): String {
        // TODO: Get from SharedPreferences or Firebase
        return "" // Return empty for now to allow spinning
    }

    /**
     * Save last spin date
     */
    private fun saveLastSpinDate(date: String) {
        // TODO: Save to SharedPreferences or Firebase
    }

    /**
     * Clear spin result
     */
    fun clearResult() {
        _spinResult.value = null
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Use points for extra spin (premium feature)
     */
    fun usePointsForExtraSpin(pointsCost: Int) {
        viewModelScope.launch {
            if (_userPoints.value >= pointsCost) {
                _userPoints.value -= pointsCost
                _canSpin.value = true
                // TODO: Update points in Firebase
            } else {
                _errorMessage.value = "نقاط غير كافية"
            }
        }
    }

    /**
     * Get total wins count
     */
    fun getTotalWins(): Int {
        return _spinHistory.value.count { it.isWin }
    }

    /**
     * Get total points earned from spins
     */
    fun getTotalPointsEarned(): Int {
        return _spinHistory.value.sumOf { it.points }
    }

    /**
     * Check if user has special privileges (admin, premium, etc.)
     */
    private fun hasSpecialPrivileges(): Boolean {
        // TODO: Check user privileges
        return false
    }

    /**
     * Get next spin availability time
     */
    fun getNextSpinTime(): String {
        if (_canSpin.value) return "متاح الآن"
        
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        
        val timeUntilNext = calendar.timeInMillis - System.currentTimeMillis()
        val hours = timeUntilNext / (1000 * 60 * 60)
        val minutes = (timeUntilNext % (1000 * 60 * 60)) / (1000 * 60)
        
        return "خلال ${hours}س ${minutes}د"
    }
}
