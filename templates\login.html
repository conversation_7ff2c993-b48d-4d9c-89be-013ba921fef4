{% extends "base.html" %}

{% block title %}تسجيل الدخول - Trust Market{% endblock %}

{% block content %}
<div class="login-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-card">
                    <div class="text-center mb-4">
                        <i class="fas fa-shield-alt login-icon"></i>
                        <h2 class="login-title">Trust Market</h2>
                        <p class="login-subtitle">منصة آمنة للبيع والشراء</p>
                    </div>

                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope"></i>
                                البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock"></i>
                                كلمة المرور
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3" id="loginBtn">
                            <i class="fas fa-sign-in-alt"></i>
                            تسجيل الدخول
                        </button>

                        <div class="text-center">
                            <a href="#" class="text-decoration-none" onclick="showRegisterForm()">
                                ليس لديك حساب؟ إنشاء حساب جديد
                            </a>
                        </div>
                    </form>

                    <!-- نموذج التسجيل -->
                    <form id="registerForm" style="display: none;">
                        <div class="text-center mb-4">
                            <h3>إنشاء حساب جديد</h3>
                        </div>

                        <div class="mb-3">
                            <label for="regName" class="form-label">
                                <i class="fas fa-user"></i>
                                الاسم
                            </label>
                            <input type="text" class="form-control" id="regName" required>
                        </div>

                        <div class="mb-3">
                            <label for="regEmail" class="form-label">
                                <i class="fas fa-envelope"></i>
                                البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="regEmail" required>
                        </div>

                        <div class="mb-3">
                            <label for="regPassword" class="form-label">
                                <i class="fas fa-lock"></i>
                                كلمة المرور
                            </label>
                            <input type="password" class="form-control" id="regPassword" required>
                        </div>

                        <div class="mb-3">
                            <label for="regConfirmPassword" class="form-label">
                                <i class="fas fa-lock"></i>
                                تأكيد كلمة المرور
                            </label>
                            <input type="password" class="form-control" id="regConfirmPassword" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                أوافق على <a href="#" onclick="showTerms()">الشروط والأحكام</a>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-success w-100 mb-3" id="registerBtn">
                            <i class="fas fa-user-plus"></i>
                            إنشاء الحساب
                        </button>

                        <div class="text-center">
                            <a href="#" class="text-decoration-none" onclick="showLoginForm()">
                                لديك حساب بالفعل؟ تسجيل الدخول
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة الشروط والأحكام -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الشروط والأحكام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>شروط الاستخدام</h6>
                <p>مرحباً بك في Trust Market، منصة آمنة للبيع والشراء. باستخدام هذه المنصة، فإنك توافق على الشروط التالية:</p>
                
                <h6>الاستخدام المسؤول</h6>
                <ul>
                    <li>يجب استخدام المنصة بطريقة مسؤولة ومحترمة</li>
                    <li>ممنوع نشر محتوى مسيء أو غير قانوني</li>
                    <li>يجب احترام جميع المستخدمين الآخرين</li>
                </ul>

                <h6>الخصوصية والأمان</h6>
                <ul>
                    <li>نحن نحترم خصوصيتك ونحمي بياناتك الشخصية</li>
                    <li>لا نشارك معلوماتك مع أطراف ثالثة دون موافقتك</li>
                    <li>يجب عليك الحفاظ على أمان حسابك وكلمة المرور</li>
                </ul>

                <h6>المسؤولية</h6>
                <ul>
                    <li>المنصة غير مسؤولة عن المعاملات بين المستخدمين</li>
                    <li>يجب على المستخدمين التحقق من صحة المعلومات قبل إتمام أي معاملة</li>
                    <li>ننصح بالحذر والتأكد من موثوقية الطرف الآخر</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تبديل عرض كلمة المرور
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // عرض نموذج التسجيل
    function showRegisterForm() {
        document.getElementById('loginForm').style.display = 'none';
        document.getElementById('registerForm').style.display = 'block';
    }

    // عرض نموذج تسجيل الدخول
    function showLoginForm() {
        document.getElementById('registerForm').style.display = 'none';
        document.getElementById('loginForm').style.display = 'block';
    }

    // عرض الشروط والأحكام
    function showTerms() {
        const modal = new bootstrap.Modal(document.getElementById('termsModal'));
        modal.show();
    }

    // معالجة تسجيل الدخول
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const loginBtn = document.getElementById('loginBtn');
        
        // تعطيل الزر أثناء المعالجة
        loginBtn.disabled = true;
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
        
        fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('تم تسجيل الدخول بنجاح!', 'success');
                setTimeout(() => {
                    window.location.href = '/chat';
                }, 1000);
            } else {
                showToast(data.error || 'خطأ في تسجيل الدخول', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول';
        });
    });

    // معالجة التسجيل
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const name = document.getElementById('regName').value;
        const email = document.getElementById('regEmail').value;
        const password = document.getElementById('regPassword').value;
        const confirmPassword = document.getElementById('regConfirmPassword').value;
        const registerBtn = document.getElementById('registerBtn');
        
        // التحقق من تطابق كلمات المرور
        if (password !== confirmPassword) {
            showToast('كلمات المرور غير متطابقة', 'error');
            return;
        }
        
        // تعطيل الزر أثناء المعالجة
        registerBtn.disabled = true;
        registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';
        
        // للتبسيط، سنقوم بتسجيل الدخول مباشرة بعد التسجيل
        fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                password: password,
                name: name
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('تم إنشاء الحساب بنجاح!', 'success');
                setTimeout(() => {
                    window.location.href = '/chat';
                }, 1000);
            } else {
                showToast(data.error || 'خطأ في إنشاء الحساب', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            registerBtn.disabled = false;
            registerBtn.innerHTML = '<i class="fas fa-user-plus"></i> إنشاء الحساب';
        });
    });
</script>
{% endblock %}
