# Trust Market - تطبيق الدردشة بـ Python

## نظرة عامة
Trust Market هو تطبيق دردشة آمن مبني بـ Python باستخدام Flask و Socket.IO. يوفر منصة آمنة للتواصل والبيع والشراء.

## الميزات الرئيسية
- 💬 دردشة فورية في الوقت الفعلي
- 🏠 غرف دردشة متعددة
- 👥 دردشات خاصة بين المستخدمين
- 🔐 نظام مصادقة آمن
- 📱 تصميم متجاوب للجوال
- 🌙 واجهة داكنة عصرية
- 🔔 إشعارات فورية
- 🌐 دعم اللغة العربية

## التقنيات المستخدمة
- **Backend**: Python, Flask, Flask-SocketIO
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Database**: Firebase Firestore
- **Real-time**: Socket.IO
- **Authentication**: Firebase Auth

## متطلبات النظام
- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- حساب Firebase

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd python-chat
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
```

### 3. تفعيل البيئة الافتراضية
**Windows:**
```bash
venv\Scripts\activate
```

**macOS/Linux:**
```bash
source venv/bin/activate
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 5. إعداد Firebase
1. إنشاء مشروع جديد في [Firebase Console](https://console.firebase.google.com/)
2. تفعيل Firestore Database
3. تفعيل Authentication
4. تحميل ملف Service Account Key
5. تحديث ملف `.env` بالمعلومات المطلوبة

### 6. تشغيل التطبيق
```bash
python app.py
```

التطبيق سيعمل على: `http://localhost:5000`

## هيكل المشروع
```
python-chat/
├── app.py                 # الملف الرئيسي للتطبيق
├── requirements.txt       # متطلبات Python
├── .env                  # متغيرات البيئة
├── README_PYTHON.md      # هذا الملف
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── login.html
│   ├── chat.html
│   └── ...
├── static/               # الملفات الثابتة
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js
│   └── images/
└── venv/                 # البيئة الافتراضية
```

## الاستخدام

### تسجيل الدخول
1. افتح التطبيق في المتصفح
2. انقر على "تسجيل الدخول"
3. أدخل بريدك الإلكتروني وكلمة المرور
4. أو انقر على "إنشاء حساب جديد"

### الدردشة
1. اختر غرفة من القائمة الجانبية
2. ابدأ بكتابة رسالتك
3. اضغط Enter أو انقر على زر الإرسال

### الدردشات الخاصة
1. انتقل إلى صفحة "الدردشات الخاصة"
2. ابحث عن مستخدم أو ابدأ محادثة جديدة
3. استمتع بالدردشة الخاصة

## الإعدادات

### متغيرات البيئة
قم بتحديث ملف `.env` بالقيم الصحيحة:

```env
# إعدادات Firebase
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_CLIENT_CERT_URL=your_client_cert_url

# إعدادات التطبيق
SECRET_KEY=your-secret-key
FLASK_ENV=production  # للإنتاج
```

## النشر

### النشر على Heroku
1. إنشاء تطبيق Heroku جديد
2. إضافة متغيرات البيئة
3. رفع الكود ونشره

### النشر على خادم Linux
1. تثبيت Python و pip
2. نسخ الملفات إلى الخادم
3. تثبيت المتطلبات
4. إعداد خدمة systemd
5. إعداد Nginx كـ reverse proxy

## الأمان
- 🔐 تشفير كلمات المرور
- 🛡️ حماية من CSRF
- 🚫 تنظيف المدخلات من XSS
- 🔒 جلسات آمنة
- 🌐 HTTPS في الإنتاج

## المساهمة
نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT.

## الدعم
للحصول على الدعم:
- 📧 البريد الإلكتروني: <EMAIL>
- 💬 الدردشة المباشرة في التطبيق
- 📱 واتساب: +964XXXXXXXXX

## التحديثات المستقبلية
- [ ] إضافة مشاركة الملفات
- [ ] إضافة الرسائل الصوتية
- [ ] إضافة مكالمات الفيديو
- [ ] إضافة البوتات الذكية
- [ ] إضافة نظام التقييمات
- [ ] إضافة المتجر المدمج

---

**Trust Market** - منصة آمنة للبيع والشراء 🛡️
