package com.trustmarket.chat.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.trustmarket.chat.R
import com.trustmarket.chat.data.model.Attachment
import kotlinx.coroutines.delay

/**
 * Chat input component for typing and sending messages
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatInput(
    onSendMessage: (String, List<Attachment>) -> Unit,
    onTyping: (Boolean) -> Unit,
    enabled: Boolean = true,
    modifier: Modifier = Modifier
) {
    var textFieldValue by remember { mutableStateOf(TextFieldValue()) }
    var showAttachmentOptions by remember { mutableStateOf(false) }
    var isRecording by remember { mutableStateOf(false) }
    val keyboardController = LocalSoftwareKeyboardController.current

    // Handle typing indicator
    LaunchedEffect(textFieldValue.text) {
        if (textFieldValue.text.isNotBlank()) {
            onTyping(true)
            delay(1000) // Stop typing after 1 second of no input
            onTyping(false)
        } else {
            onTyping(false)
        }
    }

    Column(modifier = modifier) {
        // Attachment options
        if (showAttachmentOptions) {
            AttachmentOptionsRow(
                onImageClick = { /* Handle image selection */ },
                onCameraClick = { /* Handle camera */ },
                onDocumentClick = { /* Handle document selection */ },
                onLocationClick = { /* Handle location sharing */ },
                onDismiss = { showAttachmentOptions = false }
            )
        }

        // Input row
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            verticalAlignment = Alignment.Bottom
        ) {
            // Attachment button
            IconButton(
                onClick = { showAttachmentOptions = !showAttachmentOptions },
                enabled = enabled
            ) {
                Icon(
                    imageVector = Icons.Default.AttachFile,
                    contentDescription = stringResource(id = R.string.attach_file),
                    tint = if (enabled) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                )
            }

            // Text input
            OutlinedTextField(
                value = textFieldValue,
                onValueChange = { textFieldValue = it },
                modifier = Modifier
                    .weight(1f)
                    .onFocusChanged { focusState ->
                        if (!focusState.isFocused) {
                            onTyping(false)
                        }
                    },
                placeholder = {
                    Text(text = stringResource(id = R.string.type_message))
                },
                enabled = enabled,
                maxLines = 4,
                shape = RoundedCornerShape(24.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                    unfocusedBorderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
                )
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Send/Record button
            if (textFieldValue.text.isBlank() && !isRecording) {
                // Voice record button
                IconButton(
                    onClick = { 
                        isRecording = true
                        // TODO: Start voice recording
                    },
                    enabled = enabled,
                    modifier = Modifier
                        .size(48.dp)
                        .then(
                            if (enabled) {
                                Modifier
                            } else {
                                Modifier
                            }
                        )
                ) {
                    Card(
                        modifier = Modifier.size(48.dp),
                        shape = CircleShape,
                        colors = CardDefaults.cardColors(
                            containerColor = if (enabled) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f)
                        )
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Mic,
                                contentDescription = stringResource(id = R.string.record_audio),
                                tint = if (enabled) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                            )
                        }
                    }
                }
            } else if (isRecording) {
                // Stop recording button
                IconButton(
                    onClick = { 
                        isRecording = false
                        // TODO: Stop voice recording and send
                    },
                    modifier = Modifier.size(48.dp)
                ) {
                    Card(
                        modifier = Modifier.size(48.dp),
                        shape = CircleShape,
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Stop,
                                contentDescription = "Stop Recording",
                                tint = MaterialTheme.colorScheme.onError
                            )
                        }
                    }
                }
            } else {
                // Send button
                IconButton(
                    onClick = {
                        if (textFieldValue.text.isNotBlank()) {
                            onSendMessage(textFieldValue.text.trim(), emptyList())
                            textFieldValue = TextFieldValue()
                            onTyping(false)
                            keyboardController?.hide()
                        }
                    },
                    enabled = enabled && textFieldValue.text.isNotBlank(),
                    modifier = Modifier.size(48.dp)
                ) {
                    Card(
                        modifier = Modifier.size(48.dp),
                        shape = CircleShape,
                        colors = CardDefaults.cardColors(
                            containerColor = if (enabled && textFieldValue.text.isNotBlank()) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.12f)
                            }
                        )
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Send,
                                contentDescription = stringResource(id = R.string.send),
                                tint = if (enabled && textFieldValue.text.isNotBlank()) {
                                    MaterialTheme.colorScheme.onPrimary
                                } else {
                                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                                }
                            )
                        }
                    }
                }
            }
        }

        // Recording indicator
        if (isRecording) {
            RecordingIndicator(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )
        }
    }
}

/**
 * Attachment options row
 */
@Composable
private fun AttachmentOptionsRow(
    onImageClick: () -> Unit,
    onCameraClick: () -> Unit,
    onDocumentClick: () -> Unit,
    onLocationClick: () -> Unit,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            AttachmentOption(
                icon = Icons.Default.Image,
                label = "صورة",
                onClick = onImageClick
            )
            AttachmentOption(
                icon = Icons.Default.CameraAlt,
                label = "كاميرا",
                onClick = onCameraClick
            )
            AttachmentOption(
                icon = Icons.Default.InsertDriveFile,
                label = "ملف",
                onClick = onDocumentClick
            )
            AttachmentOption(
                icon = Icons.Default.LocationOn,
                label = "موقع",
                onClick = onLocationClick
            )
        }
    }
}

/**
 * Single attachment option
 */
@Composable
private fun AttachmentOption(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        IconButton(
            onClick = onClick,
            modifier = Modifier.size(48.dp)
        ) {
            Card(
                modifier = Modifier.size(48.dp),
                shape = CircleShape,
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = label,
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }
        }
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * Recording indicator
 */
@Composable
private fun RecordingIndicator(
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Animated recording dot
        var isVisible by remember { mutableStateOf(true) }
        
        LaunchedEffect(Unit) {
            while (true) {
                delay(500)
                isVisible = !isVisible
            }
        }
        
        Box(
            modifier = Modifier
                .size(8.dp)
                .then(
                    if (isVisible) {
                        Modifier.background(
                            MaterialTheme.colorScheme.error,
                            CircleShape
                        )
                    } else {
                        Modifier
                    }
                )
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = "جاري التسجيل...",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        Text(
            text = "اسحب للإلغاء",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
    }
}
